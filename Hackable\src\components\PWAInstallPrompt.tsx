import React, { useState, useEffect } from 'react';
import { Download, X, Smartphone, Zap, Shield } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

const PWAInstallPrompt: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
    const isInWebAppiOS = (window.navigator as any).standalone === true;
    
    if (isStandalone || isInWebAppiOS) {
      setIsInstalled(true);
      return;
    }

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      
      // Show prompt after a delay to not be intrusive
      setTimeout(() => {
        const hasSeenPrompt = localStorage.getItem('pwa-install-prompt-seen');
        if (!hasSeenPrompt) {
          setShowPrompt(true);
        }
      }, 10000); // Show after 10 seconds
    };

    // Listen for app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setShowPrompt(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
      } else {
        console.log('User dismissed the install prompt');
      }
    } catch (error) {
      console.error('Error during install prompt:', error);
    }

    setDeferredPrompt(null);
    setShowPrompt(false);
    localStorage.setItem('pwa-install-prompt-seen', 'true');
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    localStorage.setItem('pwa-install-prompt-seen', 'true');
  };

  // Don't show if already installed or no prompt available
  if (isInstalled || !showPrompt || !deferredPrompt) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
      <div className={`rounded-xl p-6 shadow-2xl border transition-all duration-300 transform ${
        isDarkMode 
          ? 'bg-slate-800 border-slate-700 shadow-black/50' 
          : 'bg-white border-slate-200 shadow-slate-900/10'
      }`}>
        {/* Close button */}
        <button
          onClick={handleDismiss}
          className={`absolute top-3 right-3 p-1 rounded-lg transition-colors duration-200 ${
            isDarkMode
              ? 'text-slate-400 hover:text-white hover:bg-slate-700'
              : 'text-slate-400 hover:text-slate-600 hover:bg-slate-100'
          }`}
        >
          <X className="h-4 w-4" />
        </button>

        {/* Header */}
        <div className="flex items-center mb-4">
          <div className="flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 mr-4">
            <img src="/CyberDuckyLogo.png" alt="Hackable" className="w-8 h-8" />
          </div>
          <div>
            <h3 className={`font-bold text-lg transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}>
              Add CyberDucky
            </h3>
            <p className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-400' : 'text-slate-500'
            }`}>
              Install our app for faster access
            </p>
          </div>
        </div>

        {/* Benefits */}
        <div className="space-y-3 mb-6">
          <div className="flex items-center">
            <Zap className="h-4 w-4 text-yellow-500 mr-3" />
            <span className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              Lightning-fast loading
            </span>
          </div>
          <div className="flex items-center">
            <Shield className="h-4 w-4 text-green-500 mr-3" />
            <span className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              Works offline
            </span>
          </div>
          <div className="flex items-center">
            <Smartphone className="h-4 w-4 text-blue-500 mr-3" />
            <span className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              Native app experience
            </span>
          </div>
        </div>

        {/* Install button */}
        <button
          onClick={handleInstallClick}
          className="w-full flex items-center justify-center px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-lg font-semibold transition-all duration-200 transform hover:scale-105"
        >
          <Download className="h-5 w-5 mr-2" />
          Install App
        </button>

        <p className={`text-xs text-center mt-3 transition-colors duration-200 ${
          isDarkMode ? 'text-slate-500' : 'text-slate-400'
        }`}>
          Free • No registration required
        </p>
      </div>
    </div>
  );
};

export default PWAInstallPrompt;
