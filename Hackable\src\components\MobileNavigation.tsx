import React from 'react';
import { Home, Info, Shield, Share2 } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface MobileNavigationProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

const MobileNavigation: React.FC<MobileNavigationProps> = ({ currentPage, onNavigate }) => {
  const { isDarkMode } = useTheme();

  const navItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      href: '#home'
    },
    {
      id: 'about',
      label: 'About',
      icon: Info,
      href: '#about'
    },
    {
      id: 'security-tips',
      label: 'Tips',
      icon: Shield,
      href: '#security-tips'
    },
    {
      id: 'share',
      label: 'Share',
      icon: Share2,
      action: 'share'
    }
  ];

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Hackable - Cybersecurity Assessment',
          text: 'Check how hackable you are with this comprehensive cybersecurity assessment!',
          url: window.location.href,
        });
      } catch (error) {
        console.log('Share cancelled or failed:', error);
      }
    } else {
      // Fallback for browsers without Web Share API
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  const handleItemClick = (item: typeof navItems[0]) => {
    if (item.action === 'share') {
      handleShare();
    } else {
      onNavigate(item.id);
      window.location.hash = item.href;
    }
  };

  return (
    <nav className={`fixed bottom-0 left-0 right-0 z-50 md:hidden border-t backdrop-blur-lg transition-colors duration-200 ${
      isDarkMode 
        ? 'bg-slate-900/95 border-slate-700' 
        : 'bg-white/95 border-slate-200'
    }`}>
      <div className="flex items-center justify-around px-2 py-2 safe-area-bottom">
        {navItems.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.id;
          
          return (
            <button
              key={item.id}
              onClick={() => handleItemClick(item)}
              className={`flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 touch-manipulation ${
                isActive
                  ? isDarkMode
                    ? 'bg-blue-600 text-white'
                    : 'bg-blue-500 text-white'
                  : isDarkMode
                    ? 'text-slate-400 hover:text-white hover:bg-slate-800'
                    : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'
              }`}
            >
              <Icon className={`h-5 w-5 mb-1 ${isActive ? 'scale-110' : ''} transition-transform duration-200`} />
              <span className="text-xs font-medium">{item.label}</span>
            </button>
          );
        })}
      </div>
    </nav>
  );
};

export default MobileNavigation;
