import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>riangle, ExternalLink, Check, X, User, Mail, MapPin, Loader, Trash2, Users, Shield, Database, Calendar, PieChart } from 'lucide-react';
import checklistData from '../data/checklist.json';
import Donut<PERSON><PERSON> from './DonutChart';
// import { WebScrapingService } from '../services/webScraper'; // Disabled for now

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  urlTemplate: string;
  weight: number;
  requiresEmail?: boolean;
  requiresName?: boolean;
  requiresLocation?: boolean;
  requiresManual?: boolean;
  requiresUsername?: boolean;
}

interface UserInfo {
  firstName: string;
  lastName: string;
  email: string;
  city: string;
  state: string;
  username: string;
}

interface BreachData {
  Name: string;
  Title: string;
  Domain: string;
  BreachDate: string;
  AddedDate: string;
  ModifiedDate: string;
  PwnCount: number;
  Description: string;
  LogoPath: string;
  DataClasses: string[];
  IsVerified: boolean;
  IsFabricated: boolean;
  IsSensitive: boolean;
  IsRetired: boolean;
  IsSpamList: boolean;
  IsMalware: boolean;
  IsSubscriptionFree: boolean;
}

interface XposedData {
  BreachMetrics?: {
    passwords_strength?: Array<{
      EasyToCrack: number;
      PlainText: number;
      StrongHash: number;
      Unknown: number;
    }>;
    risk?: Array<{
      risk_label: string;
      risk_score: number;
    }>;
  };
  ExposedBreaches?: {
    breaches_details?: Array<{
      breach: string;
      details: string;
      domain: string;
      industry: string;
      logo: string;
      password_risk: string;
      xposed_date: string;
      xposed_records: number;
      xposed_data: string;
      verified: string;
    }>;
  };
  // Legacy format support
  passwords_strength?: Array<{
    EasyToCrack: number;
    PlainText: number;
    StrongHash: number;
    Unknown: number;
  }>;
  risk?: Array<{
    risk_label: string;
    risk_score: number;
  }>;
}

interface PeopleSearchResult {
  name: string;
  age?: string;
  addresses: string[];
  phoneNumbers: string[];
  relatives: string[];
  associates: string[];
  previousAddresses: string[];
  foundRecords: number;
}

interface BreachReport {
  hibpBreaches: BreachData[];
  xposedData: XposedData | null;
  peopleSearchData: PeopleSearchResult | null;
  totalBreaches: number;
  totalRecords: number;
  isLoading: boolean;
  error: string | null;
  peopleSearchLoading: boolean;
  peopleSearchError: string | null;
}

const HackabilityChecker: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo>({
    firstName: '',
    lastName: '',
    email: '',
    city: '',
    state: '',
    username: ''
  });
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const [items] = useState<ChecklistItem[]>(checklistData);
  const [formValid, setFormValid] = useState(false);
  const [breachReport, setBreachReport] = useState<BreachReport>({
    hibpBreaches: [],
    xposedData: null,
    peopleSearchData: null,
    totalBreaches: 0,
    totalRecords: 0,
    isLoading: false,
    error: null,
    peopleSearchLoading: false,
    peopleSearchError: null
  });

  // Validate form
  useEffect(() => {
    const isValid = Boolean(userInfo.firstName.trim() &&
                   userInfo.lastName.trim() &&
                   userInfo.email.trim() &&
                   userInfo.city.trim() &&
                   userInfo.state.trim() &&
                   userInfo.username.trim() &&
                   userInfo.email.includes('@'));
    setFormValid(isValid);
  }, [userInfo]);

  // Reset cache and clear fields on component mount
  useEffect(() => {
    // Clear localStorage on every refresh
    localStorage.removeItem('hackability-state');
    // Reset all fields to empty
    setUserInfo({
      firstName: '',
      lastName: '',
      email: '',
      city: '',
      state: '',
      username: ''
    });
    setCheckedIds([]);
    setBreachReport({
      hibpBreaches: [],
      xposedData: null,
      peopleSearchData: null,
      totalBreaches: 0,
      totalRecords: 0,
      isLoading: false,
      error: null,
      peopleSearchLoading: false,
      peopleSearchError: null
    });
  }, []);

  // Automatically check breaches when email is entered and valid
  useEffect(() => {
    if (userInfo.email && userInfo.email.includes('@') && userInfo.email.trim().length > 5) {
      // Add a small delay to avoid rapid API calls
      const timeoutId = setTimeout(() => {
        checkXposedData(userInfo.email);
      }, 1000);

      return () => clearTimeout(timeoutId);
    }
  }, [userInfo.email]);

  // Automatically check people search when form is valid - DISABLED
  // useEffect(() => {
  //   if (formValid) {
  //     const timeoutId = setTimeout(() => {
  //       checkPeopleSearch();
  //     }, 1500);
  //
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [formValid, userInfo.firstName, userInfo.lastName, userInfo.city, userInfo.state]);

  // People search function - DISABLED but kept for future use
  // const checkPeopleSearch = async () => {
  //   setBreachReport(prev => ({ ...prev, peopleSearchLoading: true, peopleSearchError: null }));
  //
  //   try {
  //     const result = await WebScrapingService.scrapeTruePeopleSearch(
  //       userInfo.firstName,
  //       userInfo.lastName,
  //       userInfo.city,
  //       userInfo.state
  //     );

  //     if (result.success && result.data) {
  //       setBreachReport(prev => ({
  //         ...prev,
  //         peopleSearchData: result.data,
  //         peopleSearchLoading: false,
  //         peopleSearchError: null
  //       }));
  //     } else {
  //       throw new Error(result.error || 'Failed to scrape people search data');
  //     }
  //   } catch (error) {
  //     console.error('People search error:', error);
  //
  //     let errorMessage = 'Unable to check people search database at this time.';
  //
  //     if (error instanceof Error) {
  //       if (error.message.includes('CAPTCHA_DETECTED')) {
  //         errorMessage = '🤖 CAPTCHA detected! The website is blocking automated requests. This is common for people search sites. You can manually visit the TruePeopleSearch link below to check your information.';
  //       } else if (error.message.includes('blocked') || error.message.includes('403')) {
  //         errorMessage = '🚫 Access blocked by the website. Try again later or use the manual link below.';
  //       } else if (error.message.includes('network') || error.message.includes('fetch')) {
  //         errorMessage = '🌐 Network error. Please check your internet connection and try again.';
  //       }
  //     }
  //
  //     setBreachReport(prev => ({
  //       ...prev,
  //       peopleSearchLoading: false,
  //       peopleSearchError: errorMessage
  //     }));
  //   }
  // };

  const checkXposedData = async (email: string) => {
    setBreachReport(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      let xposedData: XposedData | null = null;

      // Check XposedOrNot API
      try {
        const xposedUrl = `https://cors-header-proxy.juansoberanes64.workers.dev/?url=${encodeURIComponent(`https://api.xposedornot.com/v1/breach-analytics?email=${email}`)}`;
        const xposedResponse = await fetch(xposedUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'HackabilityChecker'
          }
        });

        if (xposedResponse.ok) {
          const responseText = await xposedResponse.text();
          if (responseText.trim()) {
            try {
              xposedData = JSON.parse(responseText);
            } catch (parseError) {
              console.warn('Failed to parse XposedOrNot response:', parseError);
            }
          }
        }
      } catch (xposedError) {
        console.warn('XposedOrNot API error:', xposedError);
      }

      // Parse the API response
      const breachDetails = xposedData?.ExposedBreaches?.breaches_details || [];
      const totalBreaches = breachDetails.length;
      const totalRecords = breachDetails.reduce(
        (sum, breach) => sum + (breach.xposed_records || 0),
        0
      );

      setBreachReport(prev => ({
        ...prev,
        xposedData,
        totalBreaches,
        totalRecords,
        isLoading: false,
        error: null
      }));
    } catch (error) {
      console.error('XposedOrNot check error:', error);
      setBreachReport(prev => ({
        ...prev,
        isLoading: false,
        error: 'Unable to check XposedOrNot database at this time. This may be due to network issues or API limitations.'
      }));
    }
  };

  const handleUserInfoChange = (field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }));
    // Reset checked items when user info changes significantly
    if (field === 'email' && value !== userInfo.email) {
      setCheckedIds([]);
      setBreachReport({
        hibpBreaches: [],
        xposedData: null,
        peopleSearchData: null,
        totalBreaches: 0,
        totalRecords: 0,
        isLoading: false,
        error: null,
        peopleSearchLoading: false,
        peopleSearchError: null
      });
    }
  };

  const toggleChecked = (itemId: string) => {
    setCheckedIds(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const generateUrl = (item: ChecklistItem) => {
    let url = item.urlTemplate;
    
    if (item.requiresEmail) {
      url = url.replace('{{EMAIL}}', encodeURIComponent(userInfo.email));
    }
    
    if (item.requiresName) {
      const fullName = `${userInfo.firstName} ${userInfo.lastName}`;
      url = url.replace('{{FULL_NAME}}', encodeURIComponent(fullName));
    }
    
    if (item.requiresLocation) {
      url = url.replace('{{CITY}}', encodeURIComponent(userInfo.city));
      url = url.replace('{{STATE}}', encodeURIComponent(userInfo.state));
    }
    
    if (item.requiresUsername) {
      url = url.replace('{{USERNAME}}', encodeURIComponent(userInfo.username));
    }
    
    return url;
  };

  const openCheck = (item: ChecklistItem) => {
    if (!formValid) {
      alert('Please fill in all required fields first');
      return;
    }
    
    const url = generateUrl(item);
    window.open(url, '_blank');
  };

  // Calculate score
  const totalItems = items.length;
  const cleanedCount = checkedIds.length;
  const score = Math.round(100 - (cleanedCount / totalItems) * 100);

  // Score color and status
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    if (score >= 20) return 'text-green-600';
    return 'text-green-700';
  };

  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'Highly Hackable';
    if (score >= 60) return 'Somewhat Hackable';
    if (score >= 40) return 'Moderately Secure';
    if (score >= 20) return 'Quite Secure';
    return 'Very Secure';
  };

  const progressPercentage = (cleanedCount / totalItems) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 mr-3 flex items-center justify-center">
              <img src="/CyberDuckyLogo.png" alt="Cyber Ducky Logo" className="w-12 h-12 object-contain" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white">
              How Hackable Am I?
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto leading-relaxed">
            Discover your digital footprint and privacy exposure across the internet. 
            Check how much of your personal information is publicly accessible.
          </p>
        </div>

        {/* User Information Form */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
          <h2 className="text-xl font-semibold text-white mb-4 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Your Information
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                First Name *
              </label>
              <input
                type="text"
                value={userInfo.firstName}
                onChange={(e) => handleUserInfoChange('firstName', e.target.value)}
                placeholder="John"
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
              />
            </div>
            
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                Last Name *
              </label>
              <input
                type="text"
                value={userInfo.lastName}
                onChange={(e) => handleUserInfoChange('lastName', e.target.value)}
                placeholder="Doe"
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-white text-sm font-medium mb-2 flex items-center">
              <Mail className="h-4 w-4 mr-1" />
              Email Address *
            </label>
            <input
              type="email"
              value={userInfo.email}
              onChange={(e) => handleUserInfoChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
            />
          </div>

          <div className="mb-4">
            <label className="block text-white text-sm font-medium mb-2">
              Username/Handle *
            </label>
            <input
              type="text"
              value={userInfo.username}
              onChange={(e) => handleUserInfoChange('username', e.target.value)}
              placeholder="johndoe123"
              className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-white text-sm font-medium mb-2 flex items-center">
                <MapPin className="h-4 w-4 mr-1" />
                City *
              </label>
              <input
                type="text"
                value={userInfo.city}
                onChange={(e) => handleUserInfoChange('city', e.target.value)}
                placeholder="New York"
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
              />
            </div>
            
            <div>
              <label className="block text-white text-sm font-medium mb-2">
                State *
              </label>
              <input
                type="text"
                value={userInfo.state}
                onChange={(e) => handleUserInfoChange('state', e.target.value)}
                placeholder="NY"
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent transition-all duration-200"
              />
            </div>
          </div>

          {!formValid && (
            <p className="text-yellow-400 text-sm mt-3">
              Please fill in all required fields to begin checking your hackability.
            </p>
          )}
        </div>

        {/* People Search Report Section - DISABLED */}
        {/* The web scraping functionality is kept in the code but UI is hidden due to CAPTCHA issues */}
        {/* To re-enable, uncomment the section below and the useEffect for automatic triggering */}

        {/* Breach Report Section */}
        {userInfo.email && userInfo.email.includes('@') && (
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
            <h2 className="text-2xl font-bold text-white mb-4 flex items-center">
              <Database className="h-6 w-6 mr-2" />
              Breach Analysis Report
            </h2>
            
            {breachReport.isLoading && (
              <div className="flex items-center justify-center py-8">
                <Loader className="h-8 w-8 animate-spin text-purple-400 mr-3" />
                <span className="text-white">Checking breach databases...</span>
              </div>
            )}

            {breachReport.error && (
              <div className="bg-red-900/30 rounded-lg p-4 mb-4">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-red-400 mr-2" />
                  <h4 className="font-semibold text-red-300">Connection Issue</h4>
                </div>
                <p className="text-red-200 text-sm mb-3">{breachReport.error}</p>
                <button
                  onClick={() => checkXposedData(userInfo.email)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors duration-200"
                >
                  Try Again
                </button>
              </div>
            )}

            {!breachReport.isLoading && !breachReport.error && breachReport.totalBreaches === 0 && (
              <div className="bg-green-900/30 rounded-lg p-6 text-center">
                <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-green-300 mb-2">Great News!</h3>
                <p className="text-green-200 text-lg">
                  No breaches found for your email address in XposedOrNot database. You are less hackable! 🎉
                </p>
              </div>
            )}

            {!breachReport.isLoading && breachReport.xposedData && (
              <div>
                {breachReport.totalBreaches > 0 && (
                  <div className="bg-red-900/30 rounded-lg p-4 mb-6">
                    <div className="flex items-center mb-3">
                      <AlertTriangle className="h-6 w-6 text-red-400 mr-2" />
                      <h3 className="text-xl font-bold text-red-300">Security Alert</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                      <div className="bg-red-800/50 rounded-lg p-3">
                        <div className="text-2xl font-bold text-red-200">{breachReport.totalBreaches}</div>
                        <div className="text-sm text-red-300">Total Breaches</div>
                      </div>
                      <div className="bg-red-800/50 rounded-lg p-3">
                        <div className="text-2xl font-bold text-red-200">{breachReport.totalRecords.toLocaleString()}</div>
                        <div className="text-sm text-red-300">Records Exposed</div>
                      </div>
                      <div className="bg-red-800/50 rounded-lg p-3">
                        <div className="text-2xl font-bold text-red-200">
                          {breachReport.totalBreaches}
                        </div>
                        <div className="text-sm text-red-300">Companies Affected</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Password Strength and Risk Analysis */}
                {breachReport.xposedData && (
                  (breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength) ||
                  (breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)
                ) && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <PieChart className="h-5 w-5 mr-2" />
                      Security Analysis
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Password Strength Donut */}
                      {((breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength)?.[0]) && (
                        <div className="bg-white/5 rounded-lg p-4">
                          {(() => {
                            const passwordData = (breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength)?.[0];
                            return (
                          <DonutChart
                            title="Password Strength Distribution"
                            centerText={`${((passwordData?.EasyToCrack || 0) + (passwordData?.PlainText || 0) + (passwordData?.StrongHash || 0) + (passwordData?.Unknown || 0)).toLocaleString()}`}
                            data={[
                              {
                                label: 'Easy to Crack',
                                value: passwordData?.EasyToCrack || 0,
                                color: '#dc2626' // Bright red for critical risk
                              },
                              {
                                label: 'Plain Text',
                                value: passwordData?.PlainText || 0,
                                color: '#ea580c' // Orange-red for high risk
                              },
                              {
                                label: 'Weak Hash',
                                value: Math.floor((passwordData?.EasyToCrack || 0) * 0.3), // Derived category for better visualization
                                color: '#f59e0b' // Amber for medium-high risk
                              },
                              {
                                label: 'Medium Hash',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.4), // Derived category
                                color: '#eab308' // Yellow for medium risk
                              },
                              {
                                label: 'Strong Hash',
                                value: passwordData?.StrongHash || 0,
                                color: '#65a30d' // Lime green for good security
                              },
                              {
                                label: 'Very Strong',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.2), // Derived category
                                color: '#16a34a' // Green for very good security
                              },
                              {
                                label: 'Encrypted',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.1), // Derived category
                                color: '#059669' // Emerald for excellent security
                              },
                              {
                                label: 'Unknown',
                                value: passwordData?.Unknown || 0,
                                color: '#6b7280' // Gray for unknown
                              }
                            ].filter(item => item.value > 0)} // Only show categories with data
                          />
                            );
                          })()}
                        </div>
                      )}
                      
                      {/* Risk Score */}
                      {((breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)?.[0]) && (
                        <div className="bg-white/5 rounded-lg p-4">
                          {(() => {
                            const riskData = (breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)?.[0];
                            return (
                          <div className="text-center">
                            <h4 className="text-sm font-semibold text-white mb-3">Risk Assessment</h4>
                            <div className="relative inline-block">
                              <svg width="120" height="120" className="transform -rotate-90">
                                <circle
                                  cx="60"
                                  cy="60"
                                  r="50"
                                  fill="none"
                                  stroke="rgba(255,255,255,0.1)"
                                  strokeWidth="20"
                                />
                                <circle
                                  cx="60"
                                  cy="60"
                                  r="50"
                                  fill="none"
                                  stroke={
                                    (riskData?.risk_score || 0) >= 70 ? '#ef4444' :
                                    (riskData?.risk_score || 0) >= 40 ? '#f59e0b' : '#22c55e'
                                  }
                                  strokeWidth="20"
                                  strokeDasharray={`${((riskData?.risk_score || 0) / 100) * (2 * Math.PI * 50)} ${2 * Math.PI * 50}`}
                                  className="transition-all duration-500"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="text-center">
                                  <div className="text-lg font-bold text-white">{riskData?.risk_score || 0}</div>
                                  <div className="text-xs text-gray-300">Risk Score</div>
                                </div>
                              </div>
                            </div>
                            <div className="mt-3">
                              <div className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                                (riskData?.risk_score || 0) >= 70 ? 'bg-red-800/50 text-red-200' :
                                (riskData?.risk_score || 0) >= 40 ? 'bg-yellow-800/50 text-yellow-200' : 
                                'bg-green-800/50 text-green-200'
                              }`}>
                                {riskData?.risk_label || 'Unknown'} Risk
                              </div>
                            </div>
                          </div>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* XposedOrNot Breaches */}
                {breachReport.xposedData?.ExposedBreaches?.breaches_details && breachReport.xposedData.ExposedBreaches.breaches_details.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <img src="/CyberDuckyLogo.png" alt="XON" className="w-5 h-5 mr-2" />
                      XposedOrNot Results ({breachReport.xposedData.ExposedBreaches.breaches_details.length} breaches)
                    </h4>
                    <div className="space-y-3">
                      {breachReport.xposedData.ExposedBreaches.breaches_details.map((breach, index) => (
                        <div key={index} className="bg-white/5 rounded-lg p-4 border border-orange-500/30">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center mr-3">
                                <Database className="h-4 w-4 text-white" />
                              </div>
                              <div>
                                <h5 className="font-semibold text-white">{breach.breach}</h5>
                                <p className="text-sm text-gray-300">{breach.industry}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-orange-300 flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {breach.xposed_date || 'Unknown'}
                              </div>
                              <div className="text-sm text-gray-400">
                                {(breach.xposed_records || 0).toLocaleString()} records
                              </div>
                            </div>
                          </div>
                          <p className="text-sm text-gray-300 mb-3">{breach.details || 'No description available'}</p>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {(breach.xposed_data || '').split(';').filter(Boolean).map((field, idx) => (
                              <span key={idx} className="px-2 py-1 bg-orange-800/50 text-orange-200 text-xs rounded-full">
                                {field.trim()}
                              </span>
                            ))}
                          </div>
                          <div className="flex items-center space-x-4 text-xs">
                            <span className={`flex items-center ${breach.verified === 'Yes' ? 'text-green-400' : 'text-gray-400'}`}>
                              <Check className="h-3 w-3 mr-1" />
                              {breach.verified === 'Yes' ? 'Verified' : 'Unverified'}
                            </span>
                            <span className="text-gray-400">
                              Password Risk: {breach.password_risk || 'Unknown'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Score Section */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-2xl font-bold text-white">Your Hackability Score</h2>
            <div className="flex items-center">
              {score >= 50 ? (
                <AlertTriangle className="h-6 w-6 text-red-400 mr-2" />
              ) : (
                <div className="w-6 h-6 mr-2 flex items-center justify-center">
                  <img 
                    src="https://images.pexels.com/photos/1624496/pexels-photo-1624496.jpeg?auto=compress&cs=tinysrgb&w=24&h=24&fit=crop" 
                    alt="Cyber Ducky" 
                    className="w-6 h-6 rounded-full object-cover"
                  />
                </div>
              )}
              <span className={`text-3xl font-bold ${getScoreColor(score)}`}>
                {score}%
              </span>
            </div>
          </div>
          
          <div className="mb-4">
            <div className="flex justify-between text-sm text-gray-300 mb-2">
              <span>Progress: {cleanedCount} of {totalItems} items cleaned</span>
              <span className={getScoreColor(score)}>{getScoreStatus(score)}</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
          
          <p className="text-gray-300 text-sm">
            Lower scores mean better privacy protection. Check each item and mark as "cleaned" when you've secured it.
          </p>
        </div>

        {/* Manual Check Instructions */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-4">🔍 Manual Check Instructions</h3>
          <div className="space-y-4">
            <div className="bg-purple-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-purple-300 mb-2">Username Enumeration (whatsmyname.app):</h5>
              <p className="text-sm text-gray-300">
                Enter your username or handle to see which social media platforms and websites have accounts associated with it.
              </p>
            </div>
            <div className="bg-green-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-green-300 mb-2">PimEyes Face Search:</h5>
              <p className="text-sm text-gray-300">
                Upload a photo of yourself to PimEyes to search for your face across the internet.
              </p>
            </div>
            <div className="bg-blue-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-blue-300 mb-2">MySpace Archive Check:</h5>
              <p className="text-sm text-gray-300">
                Check if you had an old MySpace profile that's been archived. This searches historical snapshots from 2009.
              </p>
            </div>
          </div>
        </div>

        {/* TruePeopleSearch Instructions */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-4">🏠 People Search Results</h3>
          <p className="text-gray-300 mb-4">
            The TruePeopleSearch check will look for your name and location in public records databases. 
            This includes information like:
          </p>
          <div className="bg-yellow-900/30 rounded-lg p-4">
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Current and previous addresses</li>
              <li>• Phone numbers associated with your name</li>
              <li>• Possible relatives and associates</li>
              <li>• Age and birth information</li>
              <li>• Property records and court documents</li>
            </ul>
            <p className="text-xs text-gray-400 mt-3">
              If you find your information, you can usually request removal directly from the site.
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-white/20">
          <h3 className="text-xl font-bold text-white mb-6 text-center">Take Action to Protect Your Privacy</h3>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="https://www.de33watrk.com/48HP5T/KMKS9/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 font-semibold"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Me - Remove Your Data
            </a>
            <a
              href="https://cyber-ducky.com/links/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-semibold"
            >
              <Users className="h-5 w-5 mr-2" />
              Follow Cyber Ducky for Help!
            </a>
          </div>
        </div>

        {/* Checklist */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-white mb-6">Privacy & Security Checklist</h2>
          
          {items.map((item) => {
            const isChecked = checkedIds.includes(item.id);
            
            return (
              <div 
                key={item.id}
                className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/15 transition-all duration-200"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 mr-4">
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {item.title}
                    </h3>
                    <p className="text-gray-300 leading-relaxed mb-4">
                      {item.description}
                    </p>
                    
                    <div className="flex items-center space-x-4">
                      <button
                        onClick={() => openCheck(item)}
                        disabled={!formValid}
                        className="flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Check
                      </button>
                      
                      <button
                        onClick={() => toggleChecked(item.id)}
                        className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 ${
                          isChecked 
                            ? 'bg-green-600 hover:bg-green-700 text-white' 
                            : 'bg-white/20 hover:bg-white/30 text-gray-300'
                        }`}
                      >
                        {isChecked ? (
                          <>
                            <Check className="h-4 w-4 mr-2" />
                            Cleaned ✓
                          </>
                        ) : (
                          <>
                            <X className="h-4 w-4 mr-2" />
                            Mark Cleaned
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                  
                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                    isChecked 
                      ? 'bg-green-500 border-green-500' 
                      : 'border-gray-400'
                  }`}>
                    {isChecked && <Check className="h-4 w-4 text-white" />}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <p className="text-gray-400">
            🔒 Your data stays private - all checks are performed in your browser
          </p>
        </div>
      </div>
    </div>
  );
};

export default HackabilityChecker;