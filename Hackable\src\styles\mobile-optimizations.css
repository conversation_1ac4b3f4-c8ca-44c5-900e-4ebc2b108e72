/* Mobile-First Optimizations */

/* Mobile Menu Optimizations */
@media (max-width: 768px) {
  /* Hamburger menu positioning with safe area */
  .mobile-menu-container button {
    top: env(safe-area-inset-top, 16px);
    left: env(safe-area-inset-left, 16px);
  }

  /* Menu panel with safe area support */
  .mobile-menu-container > div:last-child {
    padding-top: env(safe-area-inset-top, 0);
    padding-left: env(safe-area-inset-left, 0);
  }
}

/* Improve touch targets */
@media (max-width: 768px) {
  button,
  a,
  input,
  select,
  textarea {
    min-height: 44px;
    min-width: 44px;
  }
  
  /* Optimize form inputs for mobile */
  input[type="email"],
  input[type="text"],
  input[type="password"] {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
  }
  
  /* Better spacing for mobile */
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }
  
  /* Optimize cards for mobile */
  .card {
    margin-bottom: 16px;
    border-radius: 12px;
  }
  
  /* Mobile navigation */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 12px 16px;
    z-index: 50;
  }
  
  .dark .mobile-nav {
    background: rgba(15, 23, 42, 0.95);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  button {
    border: 2px solid currentColor;
  }
  
  .text-slate-300,
  .text-slate-400,
  .text-slate-500 {
    color: #000 !important;
  }
  
  .dark .text-slate-300,
  .dark .text-slate-400,
  .dark .text-slate-500 {
    color: #fff !important;
  }
}

/* Optimize for slow connections */
@media (prefers-reduced-data: reduce) {
  /* Disable background images */
  .bg-gradient-to-r,
  .bg-gradient-to-br {
    background-image: none !important;
    background-color: #3b82f6;
  }
  
  /* Reduce shadows */
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 500px) {
  .hero-section {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  
  .mobile-nav {
    display: none;
  }
}

/* Focus improvements for keyboard navigation */
.focus-visible:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Smooth scrolling for supported browsers */
@supports (scroll-behavior: smooth) {
  html {
    scroll-behavior: smooth;
  }
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .loading-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Intersection Observer optimizations */
.lazy-load {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.lazy-load.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* Video optimizations */
video {
  max-width: 100%;
  height: auto;
}

/* TikTok embed optimizations */
.tiktok-embed {
  max-width: 100% !important;
  margin: 0 auto;
}

@media (max-width: 480px) {
  .tiktok-embed {
    min-width: 280px !important;
  }
}

/* PWA specific styles */
@media (display-mode: standalone) {
  /* Hide browser UI elements when in PWA mode */
  .browser-only {
    display: none;
  }
  
  /* Add safe area padding for devices with notches */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Utility classes for mobile optimization */
.touch-manipulation {
  touch-action: manipulation;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Performance optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Content visibility for better performance */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}
