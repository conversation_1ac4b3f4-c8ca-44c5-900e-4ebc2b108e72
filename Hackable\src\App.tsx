import React, { useState } from 'react';
import Landing from './components/Landing';
import Hackability<PERSON>hecker from './components/HackabilityChecker';
import About from './components/About';
import SecurityTips from './components/SecurityTips';
import PWAInstallPrompt from './components/PWAInstallPrompt';
import MobileNavigation from './components/MobileNavigation';
import { ThemeProvider } from './contexts/ThemeContext';

interface UserInfo {
  firstName: string;
  lastName: string;
  email: string;
  city: string;
  state: string;
  username: string;
}

function App() {
  const [currentPage, setCurrentPage] = useState('landing');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  // Simple routing based on hash
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1);
      if (hash) {
        setCurrentPage(hash);
      } else {
        // If no hash and no user info, show landing
        setCurrentPage(userInfo ? 'home' : 'landing');
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    return () => window.removeEventListener('hashchange', handleHashChange);
  }, [userInfo]);

  const handleStartAssessment = (userData: UserInfo) => {
    setUserInfo(userData);
    setCurrentPage('home');
    window.location.hash = '#home';
  };

  const renderPage = () => {
    switch (currentPage) {
      case 'about':
        return <About />;
      case 'security-tips':
        return <SecurityTips />;
      case 'home':
        return userInfo ? <HackabilityChecker userInfo={userInfo} /> : <Landing onStartAssessment={handleStartAssessment} />;
      case 'landing':
      default:
        return <Landing onStartAssessment={handleStartAssessment} />;
    }
  };

  return (
    <ThemeProvider>
      {renderPage()}
      <MobileNavigation
        currentPage={currentPage}
        onNavigate={setCurrentPage}
      />
      <PWAInstallPrompt />
    </ThemeProvider>
  );
}

export default App;