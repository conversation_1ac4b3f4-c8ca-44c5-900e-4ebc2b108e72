import React, { useEffect, useRef } from 'react';
import { useTheme } from '../contexts/ThemeContext';

interface TikTokEmbedProps {
  videoId: string;
  username: string;
  description?: string;
  autoplay?: boolean;
  muted?: boolean;
}

const TikTokEmbed: React.FC<TikTokEmbedProps> = ({ 
  videoId, 
  username, 
  description = '',
  autoplay = true,
  muted = true 
}) => {
  const { isDarkMode } = useTheme();
  const embedRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load TikTok embed script if not already loaded
    if (!window.TikTokEmbedScript) {
      const script = document.createElement('script');
      script.src = 'https://www.tiktok.com/embed.js';
      script.async = true;
      script.onload = () => {
        window.TikTokEmbedScript = true;
        // Initialize embeds after script loads
        if (window.tiktokEmbed) {
          window.tiktokEmbed.lib.render();
        }
      };
      document.head.appendChild(script);
    } else {
      // Script already loaded, just render
      if (window.tiktokEmbed) {
        window.tiktokEmbed.lib.render();
      }
    }
  }, []);

  const embedUrl = `https://www.tiktok.com/@${username}/video/${videoId}`;

  return (
    <div 
      ref={embedRef}
      className={`rounded-xl overflow-hidden border transition-colors duration-200 ${
        isDarkMode 
          ? 'bg-slate-800 border-slate-700' 
          : 'bg-white border-slate-200'
      }`}
    >
      <blockquote 
        className="tiktok-embed" 
        cite={embedUrl}
        data-video-id={videoId}
        data-embed-from="oembed"
        style={{ 
          maxWidth: '605px', 
          minWidth: '325px',
          margin: '0 auto'
        }}
      >
        <section>
          <a 
            target="_blank" 
            title={`@${username}`}
            href={`https://www.tiktok.com/@${username}?refer=embed`}
            rel="noopener noreferrer"
            className={`flex items-center transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}
          >
            @{username}
          </a>
          
          {description && (
            <p className={`mt-2 transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              {description}
            </p>
          )}
          
          <a 
            target="_blank" 
            title="♬ original sound"
            href={embedUrl}
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-600 transition-colors duration-200"
          >
            ♬ original sound
          </a>
        </section>
      </blockquote>
    </div>
  );
};

// Extend window interface for TikTok embed script
declare global {
  interface Window {
    TikTokEmbedScript?: boolean;
    tiktokEmbed?: {
      lib: {
        render: () => void;
      };
    };
  }
}

export default TikTokEmbed;
