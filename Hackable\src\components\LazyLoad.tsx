import React, { useState, useRef, useEffect, ReactNode } from 'react';

interface LazyLoadProps {
  children: ReactNode;
  height?: number;
  offset?: number;
  placeholder?: ReactNode;
  className?: string;
}

const LazyLoad: React.FC<LazyLoadProps> = ({ 
  children, 
  height = 200, 
  offset = 100, 
  placeholder,
  className = ''
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: `${offset}px`,
        threshold: 0.1
      }
    );

    if (elementRef.current) {
      observer.observe(elementRef.current);
    }

    return () => observer.disconnect();
  }, [offset]);

  useEffect(() => {
    if (isVisible && !isLoaded) {
      // Small delay to ensure smooth loading
      const timer = setTimeout(() => {
        setIsLoaded(true);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isVisible, isLoaded]);

  const defaultPlaceholder = (
    <div 
      className={`loading-skeleton rounded-lg ${className}`}
      style={{ height: `${height}px` }}
    />
  );

  return (
    <div 
      ref={elementRef}
      className={`lazy-load ${isLoaded ? 'loaded' : ''} ${className}`}
      style={{ minHeight: isLoaded ? 'auto' : `${height}px` }}
    >
      {isLoaded ? children : (placeholder || defaultPlaceholder)}
    </div>
  );
};

export default LazyLoad;
