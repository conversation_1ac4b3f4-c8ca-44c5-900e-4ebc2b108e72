// Input validation and sanitization utilities

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  sanitizedValue: string;
}

export interface UserInfoValidation {
  firstName: ValidationResult;
  lastName: ValidationResult;
  email: ValidationResult;
  username: ValidationResult;
  city: ValidationResult;
  state: ValidationResult;
}

/**
 * Sanitize input by removing potentially dangerous characters
 */
export function sanitizeInput(input: string): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  return input
    .trim()
    // Remove HTML tags
    .replace(/<[^>]*>/g, '')
    // Remove script tags and content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    // Remove javascript: protocol
    .replace(/javascript:/gi, '')
    // Remove on* event handlers
    .replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    // Remove potentially dangerous characters
    .replace(/[<>'"&]/g, (match) => {
      const entities: { [key: string]: string } = {
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#x27;',
        '&': '&amp;'
      };
      return entities[match] || match;
    })
    // Limit length
    .substring(0, 100);
}

/**
 * Validate first name
 */
export function validateFirstName(firstName: string): ValidationResult {
  const sanitized = sanitizeInput(firstName);
  const errors: string[] = [];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('First name is required');
  } else if (sanitized.length < 2) {
    errors.push('First name must be at least 2 characters');
  } else if (sanitized.length > 50) {
    errors.push('First name must be less than 50 characters');
  } else if (!/^[a-zA-Z\s\-'\.]+$/.test(sanitized)) {
    errors.push('First name can only contain letters, spaces, hyphens, apostrophes, and periods');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate last name
 */
export function validateLastName(lastName: string): ValidationResult {
  const sanitized = sanitizeInput(lastName);
  const errors: string[] = [];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('Last name is required');
  } else if (sanitized.length < 2) {
    errors.push('Last name must be at least 2 characters');
  } else if (sanitized.length > 50) {
    errors.push('Last name must be less than 50 characters');
  } else if (!/^[a-zA-Z\s\-'\.]+$/.test(sanitized)) {
    errors.push('Last name can only contain letters, spaces, hyphens, apostrophes, and periods');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult {
  const sanitized = sanitizeInput(email).toLowerCase();
  const errors: string[] = [];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('Email address is required');
  } else if (sanitized.length > 254) {
    errors.push('Email address is too long');
  } else {
    // RFC 5322 compliant email regex (simplified)
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    if (!emailRegex.test(sanitized)) {
      errors.push('Please enter a valid email address');
    }
    
    // Additional security checks
    if (sanitized.includes('..')) {
      errors.push('Email address cannot contain consecutive dots');
    }
    
    if (sanitized.startsWith('.') || sanitized.endsWith('.')) {
      errors.push('Email address cannot start or end with a dot');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate username
 */
export function validateUsername(username: string): ValidationResult {
  const sanitized = sanitizeInput(username);
  const errors: string[] = [];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('Username is required');
  } else if (sanitized.length < 3) {
    errors.push('Username must be at least 3 characters');
  } else if (sanitized.length > 30) {
    errors.push('Username must be less than 30 characters');
  } else if (!/^[a-zA-Z0-9_\-\.]+$/.test(sanitized)) {
    errors.push('Username can only contain letters, numbers, underscores, hyphens, and periods');
  } else if (/^[_\-\.]/.test(sanitized) || /[_\-\.]$/.test(sanitized)) {
    errors.push('Username cannot start or end with special characters');
  } else if (/[_\-\.]{2,}/.test(sanitized)) {
    errors.push('Username cannot contain consecutive special characters');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate city name
 */
export function validateCity(city: string): ValidationResult {
  const sanitized = sanitizeInput(city);
  const errors: string[] = [];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('City is required');
  } else if (sanitized.length < 2) {
    errors.push('City name must be at least 2 characters');
  } else if (sanitized.length > 50) {
    errors.push('City name must be less than 50 characters');
  } else if (!/^[a-zA-Z\s\-'\.]+$/.test(sanitized)) {
    errors.push('City name can only contain letters, spaces, hyphens, apostrophes, and periods');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate state
 */
export function validateState(state: string): ValidationResult {
  const sanitized = sanitizeInput(state).toUpperCase();
  const errors: string[] = [];
  
  // US state codes and common international formats
  const validStates = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY',
    'DC', 'PR', 'VI', 'GU', 'AS', 'MP'
  ];
  
  if (!sanitized || sanitized.length === 0) {
    errors.push('State is required');
  } else if (sanitized.length === 2) {
    // Check if it's a valid US state code
    if (!validStates.includes(sanitized)) {
      errors.push('Please enter a valid US state code (e.g., CA, NY, TX)');
    }
  } else if (sanitized.length > 30) {
    errors.push('State name must be less than 30 characters');
  } else if (!/^[a-zA-Z\s\-'\.]+$/.test(sanitized)) {
    errors.push('State can only contain letters, spaces, hyphens, apostrophes, and periods');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    sanitizedValue: sanitized
  };
}

/**
 * Validate all user information
 */
export function validateUserInfo(userInfo: {
  firstName: string;
  lastName: string;
  email: string;
  username: string;
  city: string;
  state: string;
}): UserInfoValidation {
  return {
    firstName: validateFirstName(userInfo.firstName),
    lastName: validateLastName(userInfo.lastName),
    email: validateEmail(userInfo.email),
    username: validateUsername(userInfo.username),
    city: validateCity(userInfo.city),
    state: validateState(userInfo.state)
  };
}

/**
 * Check if all validations pass
 */
export function isValidUserInfo(validation: UserInfoValidation): boolean {
  return Object.values(validation).every(field => field.isValid);
}

/**
 * Get all validation errors
 */
export function getAllValidationErrors(validation: UserInfoValidation): string[] {
  return Object.values(validation).flatMap(field => field.errors);
}

/**
 * Get sanitized user info
 */
export function getSanitizedUserInfo(validation: UserInfoValidation): {
  firstName: string;
  lastName: string;
  email: string;
  username: string;
  city: string;
  state: string;
} {
  return {
    firstName: validation.firstName.sanitizedValue,
    lastName: validation.lastName.sanitizedValue,
    email: validation.email.sanitizedValue,
    username: validation.username.sanitizedValue,
    city: validation.city.sanitizedValue,
    state: validation.state.sanitizedValue
  };
}

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private readonly maxAttempts: number;
  private readonly windowMs: number;

  constructor(maxAttempts: number = 5, windowMs: number = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  isAllowed(identifier: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(identifier) || [];
    
    // Remove old attempts outside the window
    const recentAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (recentAttempts.length >= this.maxAttempts) {
      return false;
    }
    
    // Add current attempt
    recentAttempts.push(now);
    this.attempts.set(identifier, recentAttempts);
    
    return true;
  }

  getRemainingTime(identifier: string): number {
    const attempts = this.attempts.get(identifier) || [];
    if (attempts.length === 0) return 0;
    
    const oldestAttempt = Math.min(...attempts);
    const timeUntilReset = this.windowMs - (Date.now() - oldestAttempt);
    
    return Math.max(0, timeUntilReset);
  }
}
