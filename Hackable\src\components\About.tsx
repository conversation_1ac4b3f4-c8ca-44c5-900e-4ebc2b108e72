import React from 'react';
import { Shield, Search, Eye, Users, Database, Globe, Coffee, Moon, Sun } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import MobileMenu from './MobileMenu';

const About: React.FC = () => {
  const { isDarkMode, toggleDarkMode } = useTheme();

  return (
    <div className={`min-h-screen transition-colors duration-200 ${isDarkMode ? 'bg-slate-900' : 'bg-slate-50'}`}>
      {/* Mobile Menu */}
      <MobileMenu currentPage="about" onNavigate={(page) => window.location.hash = `#${page}`} />

      {/* Navbar */}
      <nav className={`border-b px-6 py-4 shadow-sm transition-colors duration-200 ${
        isDarkMode
          ? 'bg-slate-800 border-slate-700'
          : 'bg-white border-slate-200'
      }`}>
        <div className="container mx-auto max-w-6xl flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {/* Logo - centered on mobile, left-aligned on desktop */}
            <div className="flex items-center md:ml-0 ml-16">
              <img src="/CyberDuckyLogo.png" alt="Hackable Logo" className="w-8 h-8 object-contain mr-3" />
              <span className={`font-semibold text-xl tracking-tight transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Hackable</span>
            </div>
            {/* Desktop Navigation - hidden on mobile */}
            <div className="hidden md:flex space-x-8">
              <a href="#home" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Home
              </a>
              <a href="#about" className={`font-medium hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>
                About
              </a>
              <a href="#security-tips" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Security Tips
              </a>
            </div>
          </div>
          {/* Desktop Actions - hidden on mobile */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 hover:bg-slate-600 text-yellow-400'
                  : 'bg-slate-100 hover:bg-slate-200 text-slate-600'
              }`}
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
            <a
              href="https://cyber-ducky.com/links"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg transition-colors duration-200 font-medium shadow-sm"
            >
              Get Updates
            </a>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-16 max-w-5xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            About Hackable
          </h1>
          <p className={`text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light transition-colors duration-200 ${
            isDarkMode ? 'text-slate-300' : 'text-slate-600'
          }`}>
            A comprehensive privacy assessment platform that helps you understand your digital footprint
            and take control of your online presence.
          </p>
        </div>

        {/* What is Hackable Section */}
        <div className={`rounded-xl p-8 mb-12 border shadow-sm transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <h2 className={`text-3xl font-semibold mb-8 flex items-center transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            <Shield className="h-8 w-8 mr-4 text-blue-500" />
            What is Hackable?
          </h2>
          <div className={`space-y-6 leading-relaxed transition-colors duration-200 ${
            isDarkMode ? 'text-slate-300' : 'text-slate-700'
          }`}>
            <p className="text-lg">
              Hackable is a comprehensive privacy assessment platform that helps you understand your digital footprint.
              In today's interconnected world, your personal information is scattered across countless databases,
              social media platforms, and public records.
            </p>
            <p className="text-lg">
              Our mission is to empower individuals to discover what information about them is publicly accessible
              on the internet. By providing automated security checks and clear guidance, we help you take control
              of your digital privacy and protect your personal information.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-10">
              <div className={`rounded-lg p-6 border transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600'
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <Globe className="h-8 w-8 text-blue-500 mb-4" />
                <h3 className={`text-xl font-semibold mb-3 transition-colors duration-200 ${
                  isDarkMode ? 'text-white' : 'text-slate-900'
                }`}>Comprehensive Analysis</h3>
                <p className={`leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>
                  Searches across multiple databases and public records to provide a complete
                  picture of your digital footprint and online presence.
                </p>
              </div>
              <div className={`rounded-lg p-6 border transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600'
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <Database className="h-8 w-8 text-blue-500 mb-4" />
                <h3 className={`text-xl font-semibold mb-3 transition-colors duration-200 ${
                  isDarkMode ? 'text-white' : 'text-slate-900'
                }`}>Security Monitoring</h3>
                <p className={`leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>
                  Automatically checks if your email has been involved in data breaches and
                  provides detailed analysis of your security exposure.
                </p>
              </div>
              <div className={`rounded-lg p-6 border transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600'
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <Eye className="h-8 w-8 text-blue-500 mb-4" />
                <h3 className={`text-xl font-semibold mb-3 transition-colors duration-200 ${
                  isDarkMode ? 'text-white' : 'text-slate-900'
                }`}>Privacy Insights</h3>
                <p className={`leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>
                  Provides actionable insights and clear recommendations to help you understand
                  and improve your digital privacy posture.
                </p>
              </div>
              <div className={`rounded-lg p-6 border transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600'
                  : 'bg-slate-50 border-slate-200'
              }`}>
                <Users className="h-8 w-8 text-blue-500 mb-4" />
                <h3 className={`text-xl font-semibold mb-3 transition-colors duration-200 ${
                  isDarkMode ? 'text-white' : 'text-slate-900'
                }`}>Transparent Process</h3>
                <p className={`leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>
                  Community-driven development ensuring transparency and continuous improvement
                  of our privacy protection tools and methodologies.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Manual Check Instructions */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Search className="h-6 w-6 mr-2" />
            🔍 Manual Check Instructions
          </h3>
          <div className="space-y-4">
            <div className="bg-purple-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-purple-300 mb-2">Username Enumeration (whatsmyname.app):</h5>
              <p className="text-sm text-gray-300 mb-3">
                Enter your username or handle to see which social media platforms and websites have accounts associated with it.
              </p>
              <a
                href="https://whatsmyname.app/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-purple-400 hover:text-purple-300 text-sm underline"
              >
                Visit whatsmyname.app →
              </a>
            </div>
            <div className="bg-green-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-green-300 mb-2">PimEyes Face Search:</h5>
              <p className="text-sm text-gray-300">
                Upload a photo of yourself to PimEyes to search for your face across the internet.
              </p>
            </div>
            <div className="bg-blue-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-blue-300 mb-2">MySpace Archive Check:</h5>
              <p className="text-sm text-gray-300">
                Check if you had an old MySpace profile that's been archived. This searches historical snapshots from 2009.
              </p>
            </div>
          </div>
        </div>

        {/* People Search Results */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">🏠 People Search Results</h3>
          <p className="text-gray-300 mb-4">
            The TruePeopleSearch check will look for your name and location in public records databases. 
            This includes information like:
          </p>
          <div className="bg-yellow-900/30 rounded-lg p-4">
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Current and previous addresses</li>
              <li>• Phone numbers associated with your name</li>
              <li>• Possible relatives and associates</li>
              <li>• Age and birth information</li>
              <li>• Property records and court documents</li>
            </ul>
            <p className="text-xs text-gray-400 mt-3">
              If you find your information, you can usually request removal directly from the site.
            </p>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">How It Works</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">1</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Enter Your Information</h4>
                <p className="text-gray-300 text-sm">Provide basic details like name, email, and location to begin the search.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">2</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Automated Scanning</h4>
                <p className="text-gray-300 text-sm">Our system automatically checks breach databases and analyzes your digital exposure.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">3</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Manual Verification</h4>
                <p className="text-gray-300 text-sm">Follow our guided checklist to manually verify additional sources and platforms.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">4</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Take Action</h4>
                <p className="text-gray-300 text-sm">Use our recommendations and tools to remove or secure your exposed information.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center py-8 border-t border-gray-800">
          <p className="text-gray-400 mb-4">
            🔒 Your privacy is our priority - all checks are performed securely
          </p>
          <div className="flex justify-center items-center space-x-6">
            <a
              href="https://buymeacoffee.com/cyberducky"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
            >
              <Coffee className="h-5 w-5 mr-2" />
              Buy Me Coffee
            </a>
            <a
              href="https://account.venmo.com/u/cyberducky"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
            >
              Venmo: @cyberducky
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
