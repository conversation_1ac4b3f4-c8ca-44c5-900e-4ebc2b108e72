import React from 'react';
import { Shield, Search, Eye, Users, Database, Globe, Coffee } from 'lucide-react';

const About: React.FC = () => {
  return (
    <div className="min-h-screen bg-black">
      {/* Navbar */}
      <nav className="bg-black border-b border-gray-800 px-4 py-4">
        <div className="container mx-auto max-w-6xl flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center">
              <img src="/CyberDuckyLogo.png" alt="Cyber Ducky Logo" className="w-8 h-8 object-contain mr-2" />
              <span className="text-white font-bold text-lg">Hackable</span>
            </div>
            <div className="hidden md:flex space-x-6">
              <a href="#home" className="text-gray-300 hover:text-white transition-colors duration-200">
                Home
              </a>
              <a href="#about" className="text-white font-medium">
                About
              </a>
            </div>
          </div>
          <a
            href="https://cyber-ducky.com/links"
            target="_blank"
            rel="noopener noreferrer"
            className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium"
          >
            Subscribe to CyberDucky
          </a>
        </div>
      </nav>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-6">
            <div className="w-16 h-16 mr-4 flex items-center justify-center">
              <img src="/CyberDuckyLogo.png" alt="Cyber Ducky Logo" className="w-16 h-16 object-contain" />
            </div>
            <h1 className="text-4xl md:text-5xl font-black text-white">
              About Hackable
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            An Open Source Intelligence (OSINT) gathering web application designed to help you discover 
            everything about yourself that's publicly available on the internet.
          </p>
        </div>

        {/* What is Hackable Section */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-8 mb-8 border border-gray-700">
          <h2 className="text-3xl font-bold text-white mb-6 flex items-center">
            <Shield className="h-8 w-8 mr-3 text-purple-400" />
            What is Hackable?
          </h2>
          <div className="space-y-6 text-gray-300 leading-relaxed">
            <p className="text-lg">
              Hackable is a comprehensive Open Source Intelligence (OSINT) gathering platform that empowers you 
              to understand your digital footprint. In today's interconnected world, your personal information 
              is scattered across countless databases, social media platforms, and public records.
            </p>
            <p>
              Our mission is to make it easier for individuals to discover what information about them is 
              publicly accessible on the internet - completely free of charge. By aggregating data from 
              various sources and providing automated checks, we help you take control of your digital privacy.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <div className="bg-gray-800/50 rounded-lg p-6">
                <Globe className="h-8 w-8 text-blue-400 mb-3" />
                <h3 className="text-xl font-semibold text-white mb-2">Comprehensive Coverage</h3>
                <p className="text-sm text-gray-400">
                  Searches across multiple databases, social media platforms, and public records to give you 
                  a complete picture of your online presence.
                </p>
              </div>
              <div className="bg-gray-800/50 rounded-lg p-6">
                <Database className="h-8 w-8 text-green-400 mb-3" />
                <h3 className="text-xl font-semibold text-white mb-2">Breach Monitoring</h3>
                <p className="text-sm text-gray-400">
                  Automatically checks if your email has been involved in data breaches and provides 
                  detailed analysis of password security.
                </p>
              </div>
              <div className="bg-gray-800/50 rounded-lg p-6">
                <Eye className="h-8 w-8 text-yellow-400 mb-3" />
                <h3 className="text-xl font-semibold text-white mb-2">Privacy Assessment</h3>
                <p className="text-sm text-gray-400">
                  Provides actionable insights and a hackability score to help you understand and 
                  improve your digital privacy posture.
                </p>
              </div>
              <div className="bg-gray-800/50 rounded-lg p-6">
                <Users className="h-8 w-8 text-purple-400 mb-3" />
                <h3 className="text-xl font-semibold text-white mb-2">Community Driven</h3>
                <p className="text-sm text-gray-400">
                  Community-driven, ensuring transparency and continuous improvement 
                  of our privacy protection tools.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Manual Check Instructions */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4 flex items-center">
            <Search className="h-6 w-6 mr-2" />
            🔍 Manual Check Instructions
          </h3>
          <div className="space-y-4">
            <div className="bg-purple-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-purple-300 mb-2">Username Enumeration (whatsmyname.app):</h5>
              <p className="text-sm text-gray-300 mb-3">
                Enter your username or handle to see which social media platforms and websites have accounts associated with it.
              </p>
              <a
                href="https://whatsmyname.app/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-purple-400 hover:text-purple-300 text-sm underline"
              >
                Visit whatsmyname.app →
              </a>
            </div>
            <div className="bg-green-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-green-300 mb-2">PimEyes Face Search:</h5>
              <p className="text-sm text-gray-300">
                Upload a photo of yourself to PimEyes to search for your face across the internet.
              </p>
            </div>
            <div className="bg-blue-900/30 rounded-lg p-4">
              <h5 className="font-semibold text-blue-300 mb-2">MySpace Archive Check:</h5>
              <p className="text-sm text-gray-300">
                Check if you had an old MySpace profile that's been archived. This searches historical snapshots from 2009.
              </p>
            </div>
          </div>
        </div>

        {/* People Search Results */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">🏠 People Search Results</h3>
          <p className="text-gray-300 mb-4">
            The TruePeopleSearch check will look for your name and location in public records databases. 
            This includes information like:
          </p>
          <div className="bg-yellow-900/30 rounded-lg p-4">
            <ul className="text-sm text-gray-300 space-y-1">
              <li>• Current and previous addresses</li>
              <li>• Phone numbers associated with your name</li>
              <li>• Possible relatives and associates</li>
              <li>• Age and birth information</li>
              <li>• Property records and court documents</li>
            </ul>
            <p className="text-xs text-gray-400 mt-3">
              If you find your information, you can usually request removal directly from the site.
            </p>
          </div>
        </div>

        {/* How It Works */}
        <div className="bg-gray-900/50 backdrop-blur-sm rounded-2xl p-6 mb-8 border border-gray-700">
          <h3 className="text-xl font-bold text-white mb-4">How It Works</h3>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">1</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Enter Your Information</h4>
                <p className="text-gray-300 text-sm">Provide basic details like name, email, and location to begin the search.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">2</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Automated Scanning</h4>
                <p className="text-gray-300 text-sm">Our system automatically checks breach databases and analyzes your digital exposure.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">3</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Manual Verification</h4>
                <p className="text-gray-300 text-sm">Follow our guided checklist to manually verify additional sources and platforms.</p>
              </div>
            </div>
            <div className="flex items-start">
              <div className="bg-purple-600 text-white rounded-full w-8 h-8 flex items-center justify-center mr-4 mt-1 text-sm font-bold">4</div>
              <div>
                <h4 className="text-white font-semibold mb-1">Take Action</h4>
                <p className="text-gray-300 text-sm">Use our recommendations and tools to remove or secure your exposed information.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center py-8 border-t border-gray-800">
          <p className="text-gray-400 mb-4">
            🔒 Your privacy is our priority - all checks are performed securely
          </p>
          <div className="flex justify-center items-center space-x-6">
            <a
              href="https://buymeacoffee.com/cyberducky"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
            >
              <Coffee className="h-5 w-5 mr-2" />
              Buy Me Coffee
            </a>
            <a
              href="https://account.venmo.com/u/cyberducky"
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-400 hover:text-blue-300 transition-colors duration-200"
            >
              Venmo: @cyberducky
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
