import React, { useState } from 'react';
import Landing from './components/Landing';
import Hackability<PERSON>hecker from './components/HackabilityChecker';
import About from './components/About';
import SecurityTips from './components/SecurityTips';
import PWAInstallPrompt from './components/PWAInstallPrompt';
import MobileNavigation from './components/MobileNavigation';
import { ThemeProvider } from './contexts/ThemeContext';

interface UserInfo {
  firstName: string;
  lastName: string;
  email: string;
  city: string;
  state: string;
  username: string;
  termsAccepted?: boolean;
}

function App() {
  const [currentPage, setCurrentPage] = useState('landing');
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  // Simple routing based on hash
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1);


      if (hash === 'about') {
        setCurrentPage('about');
      } else if (hash === 'security-tips') {
        setCurrentPage('security-tips');
      } else if (hash === 'home') {
        if (userInfo) {
          setCurrentPage('home');
        } else {
          // Redirect to landing if no user info
          setCurrentPage('landing');
          window.location.hash = '';
        }
      } else {
        // No hash or unknown hash - show landing
        setCurrentPage('landing');
        if (hash) {
          window.location.hash = ''; // Clear invalid hash
        }
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    return () => window.removeEventListener('hashchange', handleHashChange);
  }, [userInfo]);

  const handleStartAssessment = (userData: UserInfo) => {
    setUserInfo(userData);
    setCurrentPage('home');
    // Clear any existing hash first, then set to home
    window.location.hash = '';
    setTimeout(() => {
      window.location.hash = '#home';
    }, 100);
  };

  const renderPage = () => {

    switch (currentPage) {
      case 'about':
        return <About />;
      case 'security-tips':
        return <SecurityTips />;
      case 'home':
        // Only show HackabilityChecker if we have user info
        return <HackabilityChecker userInfo={userInfo} />;
      case 'landing':
      default:
        return <Landing onStartAssessment={handleStartAssessment} />;
    }
  };

  return (
    <ThemeProvider>
      {renderPage()}
      <MobileNavigation
        currentPage={currentPage}
        onNavigate={setCurrentPage}
      />
      <PWAInstallPrompt />
    </ThemeProvider>
  );
}

export default App;