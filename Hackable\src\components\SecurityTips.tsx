import React, { useState } from 'react';
import { Brain, Shield, Lock, AlertTriangle, Smartphone, Wifi, Globe, Database, Moon, Sun } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

const SecurityTips: React.FC = () => {
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All Tips', icon: Brain },
    { id: 'passwords', label: 'Passwords', icon: Lock },
    { id: 'phishing', label: 'Phishing', icon: AlertTriangle },
    { id: 'devices', label: 'Devices', icon: Smartphone },
    { id: 'network', label: 'Network', icon: Wifi },
    { id: 'browsing', label: 'Browsing', icon: Globe },
    { id: 'data', label: 'Data', icon: Database }
  ];

  const securityTips = [
    {
      id: 1,
      category: 'passwords',
      title: 'Use a password manager',
      description: 'Password managers generate, store, and autofill strong, unique passwords for all your accounts.',
      importance: 'high'
    },
    {
      id: 2,
      category: 'passwords',
      title: 'Enable two-factor authentication (2FA)',
      description: 'Add an extra layer of security by requiring a second form of verification beyond your password.',
      importance: 'high'
    },
    {
      id: 3,
      category: 'phishing',
      title: 'Verify email sender addresses',
      description: 'Check the actual email address, not just the display name, before clicking links or downloading attachments.',
      importance: 'high'
    },
    {
      id: 4,
      category: 'phishing',
      title: 'Don\'t click suspicious links',
      description: 'Hover over links to see where they actually lead before clicking. Go directly to websites by typing the URL.',
      importance: 'high'
    },
    {
      id: 5,
      category: 'devices',
      title: 'Keep software updated',
      description: 'Install updates promptly to patch security vulnerabilities in your operating system and applications.',
      importance: 'medium'
    },
    {
      id: 6,
      category: 'devices',
      title: 'Use antivirus/anti-malware software',
      description: 'Install reputable security software and keep it updated to protect against malware.',
      importance: 'medium'
    },
    {
      id: 7,
      category: 'network',
      title: 'Use a VPN on public Wi-Fi',
      description: 'Encrypt your connection when using public networks to prevent eavesdropping and man-in-the-middle attacks.',
      importance: 'medium'
    },
    {
      id: 8,
      category: 'network',
      title: 'Secure your home Wi-Fi',
      description: 'Use WPA3 encryption, a strong password, and change default router credentials.',
      importance: 'medium'
    },
    {
      id: 9,
      category: 'browsing',
      title: 'Check for HTTPS',
      description: 'Only enter sensitive information on websites with HTTPS (look for the padlock icon).',
      importance: 'high'
    },
    {
      id: 10,
      category: 'browsing',
      title: 'Use private browsing or clear history',
      description: 'Minimize tracking and prevent others from seeing your browsing history on shared devices.',
      importance: 'low'
    },
    {
      id: 11,
      category: 'data',
      title: 'Backup important data',
      description: 'Regularly backup important files to an external drive or cloud service to protect against ransomware.',
      importance: 'high'
    },
    {
      id: 12,
      category: 'data',
      title: 'Be careful what you share online',
      description: 'Limit personal information shared on social media that could be used for identity theft or targeted attacks.',
      importance: 'medium'
    },
    {
      id: 13,
      category: 'passwords',
      title: 'Use passphrases instead of passwords',
      description: 'Create long, memorable passphrases using multiple random words with numbers and symbols.',
      importance: 'medium'
    },
    {
      id: 14,
      category: 'phishing',
      title: 'Be wary of urgent requests',
      description: 'Be suspicious of emails creating urgency or threatening negative consequences if you don\'t act immediately.',
      importance: 'high'
    },
    {
      id: 15,
      category: 'devices',
      title: 'Lock your devices',
      description: 'Use strong PINs, passwords, or biometrics, and set devices to lock automatically after a short period.',
      importance: 'medium'
    },
    {
      id: 16,
      category: 'network',
      title: 'Disable Bluetooth when not in use',
      description: 'Turn off Bluetooth when not needed to prevent unauthorized connections and BlueBorne-type attacks.',
      importance: 'low'
    },
    {
      id: 17,
      category: 'browsing',
      title: 'Use browser extensions for security',
      description: 'Consider privacy-enhancing extensions like ad blockers, script blockers, and tracker blockers.',
      importance: 'low'
    },
    {
      id: 18,
      category: 'data',
      title: 'Encrypt sensitive files',
      description: 'Use encryption for files containing personal or financial information, especially on portable devices.',
      importance: 'medium'
    }
  ];

  const filteredTips = activeCategory === 'all' 
    ? securityTips 
    : securityTips.filter(tip => tip.category === activeCategory);

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case 'high':
        return 'bg-red-500';
      case 'medium':
        return 'bg-yellow-500';
      case 'low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getImportanceLabel = (importance: string) => {
    switch (importance) {
      case 'high':
        return 'Critical';
      case 'medium':
        return 'Important';
      case 'low':
        return 'Recommended';
      default:
        return 'Recommended';
    }
  };

  const getCategoryIcon = (categoryId: string) => {
    const category = categories.find(cat => cat.id === categoryId);
    if (!category) return null;
    
    const IconComponent = category.icon;
    return <IconComponent className="h-6 w-6" />;
  };

  return (
    <div className={`min-h-screen transition-colors duration-200 ${isDarkMode ? 'bg-slate-900' : 'bg-slate-50'}`}>
      {/* Navbar */}
      <nav className={`border-b px-6 py-4 shadow-sm transition-colors duration-200 ${
        isDarkMode
          ? 'bg-slate-800 border-slate-700'
          : 'bg-white border-slate-200'
      }`}>
        <div className="container mx-auto max-w-6xl flex items-center justify-between">
          <div className="flex items-center space-x-8">
            <div className="flex items-center">
              <img src="/CyberDuckyLogo.png" alt="Hackable Logo" className="w-8 h-8 object-contain mr-3" />
              <span className={`font-semibold text-xl tracking-tight transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Hackable</span>
            </div>
            <div className="hidden md:flex space-x-8">
              <a href="#home" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Home
              </a>
              <a href="#about" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                About
              </a>
              <a href="#security-tips" className={`font-medium hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>
                Security Tips
              </a>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 hover:bg-slate-600 text-yellow-400'
                  : 'bg-slate-100 hover:bg-slate-200 text-slate-600'
              }`}
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
            <a
              href="https://cyber-ducky.com/links"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg transition-colors duration-200 font-medium shadow-sm"
            >
              Get Updates
            </a>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-16 max-w-6xl">
        {/* Header */}
        <div className="mb-12 text-center">
          <h1 className={`text-4xl md:text-5xl font-bold mb-6 tracking-tight transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            Security Tips & Best Practices
          </h1>
          <p className={`text-xl md:text-2xl max-w-4xl mx-auto leading-relaxed font-light transition-colors duration-200 ${
            isDarkMode ? 'text-slate-300' : 'text-slate-600'
          }`}>
            Protect yourself online with these essential cybersecurity tips and best practices.
            Browse by category or view all tips to strengthen your digital security posture.
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-12">
          <div className="flex flex-wrap gap-3 justify-center">
            {categories.map(category => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`flex items-center px-6 py-3 rounded-lg transition-colors duration-200 ${
                  activeCategory === category.id
                    ? 'bg-blue-600 text-white shadow-lg'
                    : isDarkMode
                      ? 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                      : 'bg-white text-slate-700 hover:bg-slate-100 border border-slate-200'
                }`}
              >
                <category.icon className="h-4 w-4 mr-2" />
                {category.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tips Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
          {filteredTips.map(tip => (
            <div key={tip.id} className={`rounded-xl overflow-hidden shadow-lg transition-all duration-200 hover:-translate-y-1 ${
              isDarkMode
                ? 'bg-slate-800 hover:shadow-blue-900/20'
                : 'bg-white hover:shadow-xl border border-slate-200'
            }`}>
              <div className="p-6">
                <div className="flex items-start mb-4">
                  <div className={`p-3 rounded-lg mr-4 transition-colors duration-200 ${
                    isDarkMode ? 'bg-slate-700' : 'bg-slate-100'
                  }`}>
                    {getCategoryIcon(tip.category)}
                  </div>
                  <div className="flex-1">
                    <h3 className={`text-lg font-bold mb-2 transition-colors duration-200 ${
                      isDarkMode ? 'text-white' : 'text-slate-900'
                    }`}>{tip.title}</h3>
                    <div className="flex items-center">
                      <span className={`inline-block w-2 h-2 rounded-full mr-2 ${getImportanceColor(tip.importance)}`}></span>
                      <span className={`text-xs font-medium transition-colors duration-200 ${
                        isDarkMode ? 'text-slate-400' : 'text-slate-500'
                      }`}>{getImportanceLabel(tip.importance)}</span>
                    </div>
                  </div>
                </div>
                <p className={`leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>{tip.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Cybersecurity Mindset Section */}
        <div className={`rounded-xl p-8 transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border border-slate-700'
            : 'bg-white border border-slate-200'
        }`}>
          <h3 className={`text-2xl font-bold mb-6 flex items-center transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            <Shield className="h-6 w-6 text-blue-500 mr-3" />
            The Cybersecurity Mindset
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className={`text-lg font-bold mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Think Before You Click</h4>
              <p className={`mb-6 leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Develop a habit of pausing and evaluating before clicking links, downloading files, or providing information.
                Ask yourself: "Was I expecting this? Does this seem legitimate? What happens if I click this?"
              </p>
              <h4 className={`text-lg font-bold mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Update Regularly</h4>
              <p className={`leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Make updating your devices and software a routine practice. Security patches are released to fix vulnerabilities
                that hackers can exploit. Delaying updates leaves you exposed to known risks.
              </p>
            </div>
            <div>
              <h4 className={`text-lg font-bold mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Assume Breach Mentality</h4>
              <p className={`mb-6 leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Operate under the assumption that breaches can happen despite precautions. This mindset encourages
                implementing multiple layers of security and having recovery plans in place.
              </p>
              <h4 className={`text-lg font-bold mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Verify, Then Trust</h4>
              <p className={`leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Always verify the identity of people, organizations, and websites before sharing sensitive information.
                Use official channels and contact methods rather than those provided in unsolicited communications.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityTips;