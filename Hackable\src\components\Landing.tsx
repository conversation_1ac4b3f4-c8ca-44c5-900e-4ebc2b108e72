import React, { useState } from 'react';
import { <PERSON>, ArrowRight, Users, Target, Award, Moon, Sun, Menu } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import MobileMenu from './MobileMenu';
import Terms from './Terms';
import {
  validateUserInfo,
  isValidUserInfo,
  getSanitizedUserInfo,
  getAllValidationErrors
} from '../utils/validation';

interface UserInfo {
  firstName: string;
  lastName: string;
  email: string;
  city: string;
  state: string;
  username: string;
  termsAccepted?: boolean;
}

interface LandingProps {
  onStartAssessment: (userInfo: UserInfo) => void;
}

const Landing: React.FC<LandingProps> = ({ onStartAssessment }) => {
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [userInfo, setUserInfo] = useState<UserInfo>({
    firstName: '',
    lastName: '',
    email: '',
    city: '',
    state: '',
    username: ''
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showTerms, setShowTerms] = useState(false);

  const handleInputChange = (field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }));
    if (showValidationErrors) {
      const updatedUserInfo = { ...userInfo, [field]: value };
      const validation = validateUserInfo(updatedUserInfo);
      const errors = getAllValidationErrors(validation);
      setValidationErrors(errors);
    }
  };

  const handleStartAssessment = () => {


    if (!termsAccepted) {
      alert('Please accept the Terms and Conditions to proceed.');
      return;
    }

    // Validate user info
    const validation = validateUserInfo(userInfo);
    const errors = getAllValidationErrors(validation);
    setValidationErrors(errors);
    setShowValidationErrors(true);

    if (errors.length === 0) {
      const sanitizedInfo = getSanitizedUserInfo(validation);
      // Include terms acceptance in the user data
      const userDataWithTerms = {
        ...sanitizedInfo,
        termsAccepted: true
      };
      onStartAssessment(userDataWithTerms);
    } else {

    }
  };

  const stats = [
    { icon: Users, label: 'Users Protected', value: '50,000+' },
    { icon: Shield, label: 'Threats Detected', value: '1M+' },
    { icon: Target, label: 'Accuracy Rate', value: '99.9%' },
    { icon: Award, label: 'Security Score', value: 'A+' }
  ];

  return (
    <div className={`min-h-screen transition-all duration-500 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900' 
        : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      {/* Mobile Menu */}
      <MobileMenu currentPage="home" onNavigate={(page) => window.location.hash = `#${page}`} />
      
      {/* Header */}
      <header className={`border-b backdrop-blur-sm transition-all duration-200 ${
        isDarkMode 
          ? 'bg-slate-900/50 border-slate-700/50' 
          : 'bg-white/50 border-slate-200/50'
      }`}>
        <div className="container mx-auto max-w-6xl px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              {/* Logo - centered on mobile, left-aligned on desktop */}
              <div className="flex items-center md:ml-0 ml-16">
                <img src="/CyberDuckyLogo.png" alt="Hackable Logo" className="w-8 h-8 object-contain mr-3" />
                <span className={`font-bold text-xl tracking-tight transition-colors duration-200 ${
                  isDarkMode ? 'text-white' : 'text-slate-900'
                }`}>Hackable</span>
              </div>
              
              {/* Desktop Navigation - hidden on mobile */}
              <nav className="hidden md:flex space-x-8">
                <a href="#about" className={`font-medium transition-colors duration-200 hover:text-blue-600 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>About</a>
                <a href="#security-tips" className={`font-medium transition-colors duration-200 hover:text-blue-600 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>Security Tips</a>
              </nav>
            </div>
            
            {/* Desktop Actions - hidden on mobile */}
            <div className="hidden md:flex items-center space-x-4">
              <button
                onClick={toggleDarkMode}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode 
                    ? 'bg-slate-700 hover:bg-slate-600 text-yellow-400' 
                    : 'bg-slate-100 hover:bg-slate-200 text-slate-600'
                }`}
                aria-label="Toggle dark mode"
              >
                {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </button>
              <a
                href="https://cyber-ducky.com/links"
                target="_blank"
                rel="noopener noreferrer"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg transition-all duration-200 font-medium shadow-sm hover:shadow-md"
              >
                Get Updates
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto max-w-4xl px-6 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="mb-8">
            <h1 className={`text-5xl md:text-6xl font-bold mb-6 transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}>
              How <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Hackable
              </span> Are You?
            </h1>
            <p className={`text-xl md:text-2xl mb-8 transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              Discover your cybersecurity vulnerabilities with our comprehensive assessment
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className={`p-6 rounded-xl border transition-all duration-200 ${
                  isDarkMode 
                    ? 'bg-slate-800/50 border-slate-700/50 backdrop-blur-sm' 
                    : 'bg-white/50 border-slate-200/50 backdrop-blur-sm'
                }`}>
                  <Icon className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                  <div className={`text-2xl font-bold mb-1 transition-colors duration-200 ${
                    isDarkMode ? 'text-white' : 'text-slate-900'
                  }`}>
                    {stat.value}
                  </div>
                  <div className={`text-sm transition-colors duration-200 ${
                    isDarkMode ? 'text-slate-400' : 'text-slate-500'
                  }`}>
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Assessment Form */}
        <div className={`max-w-2xl mx-auto p-8 rounded-2xl border shadow-xl transition-all duration-200 ${
          isDarkMode 
            ? 'bg-slate-800/50 border-slate-700/50 backdrop-blur-sm' 
            : 'bg-white/50 border-slate-200/50 backdrop-blur-sm'
        }`}>
          <div className="text-center mb-8">
            <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h2 className={`text-2xl font-bold mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}>
              Start Your Security Assessment
            </h2>
            <p className={`transition-colors duration-200 ${
              isDarkMode ? 'text-slate-400' : 'text-slate-500'
            }`}>
              Enter your information to begin your personalized cybersecurity evaluation
            </p>
          </div>

          <div className="space-y-6">
            {/* Name Fields */}
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-700'
                }`}>
                  First Name *
                </label>
                <input
                  type="text"
                  value={userInfo.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  placeholder="Enter your first name"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-700'
                }`}>
                  Last Name *
                </label>
                <input
                  type="text"
                  value={userInfo.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            {/* Email */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                Email Address *
              </label>
              <input
                type="email"
                value={userInfo.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                  isDarkMode 
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                placeholder="Enter your email address"
              />
            </div>

            {/* Location */}
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-700'
                }`}>
                  City *
                </label>
                <input
                  type="text"
                  value={userInfo.city}
                  onChange={(e) => handleInputChange('city', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  placeholder="Enter your city"
                />
              </div>
              <div>
                <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-700'
                }`}>
                  State *
                </label>
                <input
                  type="text"
                  value={userInfo.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                      : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  placeholder="Enter your state"
                />
              </div>
            </div>

            {/* Username */}
            <div>
              <label className={`block text-sm font-medium mb-2 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                Username (Optional)
              </label>
              <input
                type="text"
                value={userInfo.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                className={`w-full px-4 py-3 rounded-lg border transition-all duration-200 ${
                  isDarkMode 
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400 focus:border-blue-500' 
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-500 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                placeholder="Enter your username"
              />
            </div>

            {/* Validation Errors */}
            {showValidationErrors && validationErrors.length > 0 && (
              <div className={`border rounded-lg p-4 ${
                isDarkMode
                  ? 'bg-red-900/20 border-red-800 text-red-300'
                  : 'bg-red-50 border-red-200 text-red-700'
              }`}>
                <h4 className="font-medium mb-2">Please fix the following errors:</h4>
                <ul className="text-sm space-y-1">
                  {validationErrors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Terms and Conditions */}
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                id="terms"
                checked={termsAccepted}
                onChange={(e) => setTermsAccepted(e.target.checked)}
                className={`w-4 h-4 text-blue-600 rounded focus:ring-blue-500 focus:ring-2 mt-0.5 transition-colors duration-200 ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600'
                    : 'bg-white border-slate-300'
                }`}
              />
              <label htmlFor="terms" className={`text-sm leading-relaxed transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                I understand that my information will be checked against public databases and agree to the{' '}
                <button
                  onClick={() => setShowTerms(true)}
                  className={`underline font-medium transition-colors duration-200 ${
                    isDarkMode
                      ? 'text-blue-400 hover:text-blue-300'
                      : 'text-blue-600 hover:text-blue-700'
                  }`}
                >
                  Terms and Conditions
                </button>
              </label>
            </div>

            {/* Start Assessment Button */}
            <button
              onClick={handleStartAssessment}
              disabled={!termsAccepted}
              className={`w-full px-8 py-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center justify-center ${
                termsAccepted
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white'
                  : 'bg-slate-300 text-slate-500 cursor-not-allowed'
              }`}
            >
              Start Security Assessment
              <ArrowRight className="h-5 w-5 ml-2" />
            </button>
          </div>
        </div>
      </main>

      {/* Terms Modal */}
      <Terms isOpen={showTerms} onClose={() => setShowTerms(false)} />
    </div>
  );
};

export default Landing;
