import { useState, useEffect } from 'react';

interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isSafari: boolean;
  isChrome: boolean;
  isFirefox: boolean;
  isEdge: boolean;
  supportsWebShare: boolean;
  supportsServiceWorker: boolean;
  connectionType: string;
  isSlowConnection: boolean;
  prefersReducedMotion: boolean;
  prefersReducedData: boolean;
  screenWidth: number;
  screenHeight: number;
  devicePixelRatio: number;
}

export const useDeviceDetection = (): DeviceInfo => {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    isIOS: false,
    isAndroid: false,
    isSafari: false,
    isChrome: false,
    isFirefox: false,
    isEdge: false,
    supportsWebShare: false,
    supportsServiceWorker: false,
    connectionType: 'unknown',
    isSlowConnection: false,
    prefersReducedMotion: false,
    prefersReducedData: false,
    screenWidth: 1920,
    screenHeight: 1080,
    devicePixelRatio: 1,
  });

  useEffect(() => {
    const updateDeviceInfo = () => {
      const userAgent = navigator.userAgent;
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;
      const devicePixelRatio = window.devicePixelRatio || 1;

      // Device type detection
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) || screenWidth < 768;
      const isTablet = /iPad|Android/i.test(userAgent) && screenWidth >= 768 && screenWidth < 1024;
      const isDesktop = !isMobile && !isTablet;

      // OS detection
      const isIOS = /iPad|iPhone|iPod/.test(userAgent);
      const isAndroid = /Android/.test(userAgent);

      // Browser detection
      const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
      const isChrome = /Chrome/.test(userAgent) && /Google Inc/.test(navigator.vendor);
      const isFirefox = /Firefox/.test(userAgent);
      const isEdge = /Edg/.test(userAgent);

      // Feature detection
      const supportsWebShare = 'share' in navigator;
      const supportsServiceWorker = 'serviceWorker' in navigator;

      // Connection detection
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
      const connectionType = connection?.effectiveType || 'unknown';
      const isSlowConnection = connectionType === 'slow-2g' || connectionType === '2g';

      // User preferences
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      const prefersReducedData = window.matchMedia('(prefers-reduced-data: reduce)').matches;

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        isIOS,
        isAndroid,
        isSafari,
        isChrome,
        isFirefox,
        isEdge,
        supportsWebShare,
        supportsServiceWorker,
        connectionType,
        isSlowConnection,
        prefersReducedMotion,
        prefersReducedData,
        screenWidth,
        screenHeight,
        devicePixelRatio,
      });
    };

    updateDeviceInfo();

    // Listen for orientation changes
    const handleOrientationChange = () => {
      setTimeout(updateDeviceInfo, 100); // Small delay to ensure screen dimensions are updated
    };

    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', updateDeviceInfo);

    // Listen for connection changes
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    if (connection) {
      connection.addEventListener('change', updateDeviceInfo);
    }

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange);
      window.removeEventListener('resize', updateDeviceInfo);
      if (connection) {
        connection.removeEventListener('change', updateDeviceInfo);
      }
    };
  }, []);

  return deviceInfo;
};

// Utility functions for adaptive content delivery
export const getOptimalImageFormat = (deviceInfo: DeviceInfo): string => {
  if (deviceInfo.isSlowConnection || deviceInfo.prefersReducedData) {
    return 'webp'; // Smaller file size
  }
  
  if (deviceInfo.isSafari) {
    return 'heic'; // Better compression on Safari
  }
  
  return 'webp'; // Default to WebP for modern browsers
};

export const getOptimalVideoQuality = (deviceInfo: DeviceInfo): string => {
  if (deviceInfo.isSlowConnection || deviceInfo.prefersReducedData) {
    return '480p';
  }
  
  if (deviceInfo.isMobile) {
    return '720p';
  }
  
  if (deviceInfo.screenWidth >= 1920) {
    return '1080p';
  }
  
  return '720p';
};

export const shouldAutoplayVideo = (deviceInfo: DeviceInfo): boolean => {
  // Don't autoplay on slow connections or if user prefers reduced data
  if (deviceInfo.isSlowConnection || deviceInfo.prefersReducedData) {
    return false;
  }
  
  // iOS Safari has restrictions on autoplay
  if (deviceInfo.isIOS && deviceInfo.isSafari) {
    return false;
  }
  
  return true;
};

export const getOptimalChunkSize = (deviceInfo: DeviceInfo): number => {
  if (deviceInfo.isSlowConnection) {
    return 5; // Load fewer items at once
  }
  
  if (deviceInfo.isMobile) {
    return 10;
  }
  
  return 20; // Desktop can handle more
};

export const shouldLazyLoad = (deviceInfo: DeviceInfo): boolean => {
  // Always lazy load on mobile or slow connections
  return deviceInfo.isMobile || deviceInfo.isSlowConnection || deviceInfo.prefersReducedData;
};
