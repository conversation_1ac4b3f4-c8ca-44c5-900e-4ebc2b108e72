import React from 'react';
import { X, Shield, AlertTriangle, Database, Globe } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface TermsProps {
  isOpen: boolean;
  onClose: () => void;
}

const Terms: React.FC<TermsProps> = ({ isOpen, onClose }) => {
  const { isDarkMode } = useTheme();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div className={`rounded-2xl max-w-4xl max-h-[90vh] overflow-y-auto border transition-colors duration-200 ${
        isDarkMode
          ? 'bg-slate-800 border-slate-700'
          : 'bg-white border-slate-200'
      }`}>
        <div className={`sticky top-0 border-b p-6 flex items-center justify-between transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <h2 className={`text-2xl font-bold transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>Terms and Conditions</h2>
          <button
            onClick={onClose}
            className={`transition-colors duration-200 ${
              isDarkMode
                ? 'text-slate-400 hover:text-white'
                : 'text-slate-400 hover:text-slate-600'
            }`}
          >
            <X className="h-6 w-6" />
          </button>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Introduction */}
          <div className={`rounded-lg p-4 transition-colors duration-200 ${
            isDarkMode
              ? 'bg-blue-900/30 border border-blue-800'
              : 'bg-blue-50 border border-blue-200'
          }`}>
            <div className="flex items-center mb-3">
              <Shield className="h-6 w-6 text-blue-500 mr-2" />
              <h3 className={`text-xl font-semibold transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Data Usage Agreement</h3>
            </div>
            <p className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-700'
            }`}>
              By using Hackable, you acknowledge and agree to the following terms regarding the collection,
              processing, and transmission of your personal information for security assessment purposes.
            </p>
          </div>

          {/* Data Collection */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Database className="h-5 w-5 mr-2 text-blue-400" />
              Data Collection and Processing
            </h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p>
                <strong>Information Collected:</strong> We collect the personal information you provide, including but not limited to:
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>Full name (first and last name)</li>
                <li>Email address</li>
                <li>Username/handle</li>
                <li>City and state of residence</li>
                <li>Any additional information you voluntarily provide</li>
              </ul>
              <p>
                <strong>Purpose:</strong> This information is used solely for the purpose of conducting Open Source Intelligence (OSINT) 
                searches to assess your digital footprint and privacy exposure.
              </p>
            </div>
          </div>

          {/* Third Party Services */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
              <Globe className="h-5 w-5 mr-2 text-green-400" />
              Third-Party Service Integration
            </h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p>
                <strong>External APIs and Services:</strong> Your information may be transmitted to and processed by the following third-party services:
              </p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li><strong>HaveIBeenPwned API:</strong> To check if your email has been involved in data breaches</li>
                <li><strong>XposedOrNot API:</strong> To analyze breach exposure and password security</li>
                <li><strong>TruePeopleSearch:</strong> To search public records databases</li>
                <li><strong>WhatsMyName.app:</strong> For username enumeration across platforms</li>
                <li><strong>PimEyes:</strong> For facial recognition searches (if you choose to use this feature)</li>
                <li><strong>Other OSINT platforms:</strong> As needed for comprehensive privacy assessment</li>
              </ul>
              <p>
                <strong>Data Transmission:</strong> When you click "Find Me," your information will be automatically sent to these services 
                to perform the requested searches. Each service has its own privacy policy and data handling practices.
              </p>
            </div>
          </div>

          {/* User Rights and Responsibilities */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-yellow-400" />
              Your Rights and Responsibilities
            </h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p><strong>Consent:</strong> By checking the terms box and clicking "Find Me," you explicitly consent to:</p>
              <ul className="list-disc list-inside ml-4 space-y-1">
                <li>The collection and processing of your personal information</li>
                <li>The transmission of your data to third-party services</li>
                <li>The use of your information for OSINT research purposes</li>
                <li>The potential storage of search results for analysis</li>
              </ul>
              
              <p><strong>Accuracy:</strong> You warrant that all information provided is accurate and that you have the right to authorize these searches.</p>
              
              <p><strong>Withdrawal:</strong> You may withdraw your consent at any time by closing this application, though data already transmitted to third parties will be subject to their respective policies.</p>
            </div>
          </div>

          {/* Data Security */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Data Security and Retention</h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p>
                <strong>Local Processing:</strong> Where possible, searches are performed locally in your browser to minimize data exposure.
              </p>
              <p>
                <strong>No Long-term Storage:</strong> Hackable does not permanently store your personal information on our servers. 
                Search results may be temporarily cached for performance purposes but are not retained long-term.
              </p>
              <p>
                <strong>Third-Party Policies:</strong> Data transmitted to third-party services is subject to their respective privacy 
                policies and retention practices, which are beyond our control.
              </p>
            </div>
          </div>

          {/* Disclaimers */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Disclaimers and Limitations</h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p>
                <strong>Educational Purpose:</strong> This tool is provided for educational and privacy awareness purposes only.
              </p>
              <p>
                <strong>No Guarantees:</strong> We make no guarantees about the accuracy, completeness, or timeliness of search results.
              </p>
              <p>
                <strong>Third-Party Responsibility:</strong> We are not responsible for the data handling practices of third-party services 
                or any consequences arising from their use of your information.
              </p>
              <p>
                <strong>Legal Compliance:</strong> You are responsible for ensuring that your use of this service complies with all 
                applicable laws and regulations in your jurisdiction.
              </p>
            </div>
          </div>

          {/* Contact and Updates */}
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Contact and Updates</h4>
            <div className="space-y-3 text-gray-300 text-sm">
              <p>
                <strong>Questions:</strong> If you have questions about these terms or our data practices, please contact us through 
                our official channels at <a href="https://cyber-ducky.com/links" className="text-purple-400 hover:text-purple-300 underline">cyber-ducky.com</a>.
              </p>
              <p>
                <strong>Updates:</strong> These terms may be updated periodically. Continued use of the service constitutes acceptance of any changes.
              </p>
              <p className="text-xs text-gray-400 mt-4">
                Last updated: {new Date().toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t border-gray-700">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Terms;
