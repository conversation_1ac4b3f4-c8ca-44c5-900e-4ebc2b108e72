import React, { useState } from 'react';
import { Share2, Instagram, Video, Download, Smartphone } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface SocialShareProps {
  title: string;
  description: string;
  url?: string;
  score?: number;
}

const SocialShare: React.FC<SocialShareProps> = ({ title, description, url, score }) => {
  const { isDarkMode } = useTheme();
  const [isSharing, setIsSharing] = useState(false);

  // Web Share API for native sharing
  const handleNativeShare = async () => {
    if (navigator.share) {
      try {
        setIsSharing(true);
        await navigator.share({
          title: title,
          text: description,
          url: url || window.location.href,
        });
      } catch (error) {
        console.log('Share cancelled or failed:', error);
      } finally {
        setIsSharing(false);
      }
    } else {
      // Fallback for browsers without Web Share API
      handleCopyLink();
    }
  };

  // Instagram Stories deep link
  const handleInstagramStory = () => {
    const text = encodeURIComponent(`${title}\n\n${description}\n\nCheck your hackability: ${url || window.location.href}`);
    
    // Detect mobile platform
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isAndroid = /Android/.test(navigator.userAgent);
    
    if (isIOS) {
      // iOS Instagram Stories deep link
      window.location.href = `instagram-stories://share?text=${text}`;
    } else if (isAndroid) {
      // Android Instagram intent
      window.location.href = `intent://share?text=${text}#Intent;package=com.instagram.android;scheme=https;end`;
    } else {
      // Desktop fallback - open Instagram web
      window.open('https://www.instagram.com/', '_blank');
    }
  };

  // TikTok sharing
  const handleTikTokShare = () => {
    const text = encodeURIComponent(`${title} - ${description}`);
    const shareUrl = encodeURIComponent(url || window.location.href);
    
    // TikTok doesn't have a direct share API, so we'll open their upload page
    window.open(`https://www.tiktok.com/upload?text=${text}&url=${shareUrl}`, '_blank');
  };

  // Copy link fallback
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(url || window.location.href);
      alert('Link copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  // Generate shareable image/screenshot
  const handleGenerateShareable = async () => {
    try {
      setIsSharing(true);
      
      // Create a canvas with the user's score and branding
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) return;
      
      canvas.width = 1080;
      canvas.height = 1920; // TikTok/Instagram Stories aspect ratio
      
      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
      gradient.addColorStop(0, isDarkMode ? '#0f172a' : '#f8fafc');
      gradient.addColorStop(1, isDarkMode ? '#1e293b' : '#e2e8f0');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      // Title
      ctx.fillStyle = isDarkMode ? '#ffffff' : '#1e293b';
      ctx.font = 'bold 72px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Hackable', canvas.width / 2, 200);
      
      // Score
      if (score !== undefined) {
        ctx.font = 'bold 120px Arial';
        ctx.fillStyle = score >= 80 ? '#10b981' : score >= 60 ? '#f59e0b' : '#ef4444';
        ctx.fillText(`${score}%`, canvas.width / 2, 400);
        
        ctx.font = '48px Arial';
        ctx.fillStyle = isDarkMode ? '#cbd5e1' : '#64748b';
        ctx.fillText('Security Score', canvas.width / 2, 480);
      }
      
      // Description
      ctx.font = '36px Arial';
      ctx.fillStyle = isDarkMode ? '#cbd5e1' : '#64748b';
      const words = description.split(' ');
      let line = '';
      let y = 600;
      
      for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n] + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;
        
        if (testWidth > canvas.width - 100 && n > 0) {
          ctx.fillText(line, canvas.width / 2, y);
          line = words[n] + ' ';
          y += 50;
        } else {
          line = testLine;
        }
      }
      ctx.fillText(line, canvas.width / 2, y);
      
      // URL
      ctx.font = '32px Arial';
      ctx.fillStyle = '#3b82f6';
      ctx.fillText(url || window.location.href, canvas.width / 2, canvas.height - 100);
      
      // Convert to blob and download
      canvas.toBlob((blob) => {
        if (blob) {
          const url = URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = 'hackable-score.png';
          a.click();
          URL.revokeObjectURL(url);
        }
      }, 'image/png');
      
    } catch (error) {
      console.error('Failed to generate shareable:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className={`rounded-xl p-6 border transition-colors duration-200 ${
      isDarkMode 
        ? 'bg-slate-800 border-slate-700' 
        : 'bg-white border-slate-200'
    }`}>
      <h3 className={`text-lg font-semibold mb-4 flex items-center transition-colors duration-200 ${
        isDarkMode ? 'text-white' : 'text-slate-900'
      }`}>
        <Share2 className="h-5 w-5 mr-2 text-blue-500" />
        Share Your Results
      </h3>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        {/* Native Share */}
        <button
          onClick={handleNativeShare}
          disabled={isSharing}
          className={`flex flex-col items-center p-4 rounded-lg transition-colors duration-200 ${
            isDarkMode
              ? 'bg-slate-700 hover:bg-slate-600 text-slate-300'
              : 'bg-slate-100 hover:bg-slate-200 text-slate-700'
          } ${isSharing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Smartphone className="h-6 w-6 mb-2" />
          <span className="text-sm font-medium">Share</span>
        </button>

        {/* Instagram Stories */}
        <button
          onClick={handleInstagramStory}
          className={`flex flex-col items-center p-4 rounded-lg transition-colors duration-200 ${
            isDarkMode
              ? 'bg-gradient-to-br from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700'
              : 'bg-gradient-to-br from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600'
          } text-white`}
        >
          <Instagram className="h-6 w-6 mb-2" />
          <span className="text-sm font-medium">Stories</span>
        </button>

        {/* TikTok */}
        <button
          onClick={handleTikTokShare}
          className="flex flex-col items-center p-4 rounded-lg bg-black hover:bg-gray-800 text-white transition-colors duration-200"
        >
          <Video className="h-6 w-6 mb-2" />
          <span className="text-sm font-medium">TikTok</span>
        </button>

        {/* Generate Shareable */}
        <button
          onClick={handleGenerateShareable}
          disabled={isSharing}
          className={`flex flex-col items-center p-4 rounded-lg transition-colors duration-200 ${
            isDarkMode
              ? 'bg-blue-600 hover:bg-blue-700'
              : 'bg-blue-500 hover:bg-blue-600'
          } text-white ${isSharing ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <Download className="h-6 w-6 mb-2" />
          <span className="text-sm font-medium">Image</span>
        </button>
      </div>
      
      <p className={`text-xs mt-3 text-center transition-colors duration-200 ${
        isDarkMode ? 'text-slate-400' : 'text-slate-500'
      }`}>
        Share your cybersecurity score and help others stay safe online!
      </p>
    </div>
  );
};

export default SocialShare;
