import React from 'react';

interface DonutChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  title: string;
  centerText?: string;
  size?: number;
}

const DonutChart: React.FC<DonutChartProps> = ({ 
  data, 
  title, 
  centerText, 
  size = 120 
}) => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  if (total === 0) {
    return (
      <div className="text-center bg-slate-50 rounded-lg p-6 border border-slate-200">
        <h4 className="text-sm font-semibold text-slate-900 mb-2">{title}</h4>
        <div className="text-slate-500 text-xs">No data available</div>
      </div>
    );
  }

  let cumulativePercentage = 0;
  const radius = size / 2 - 10;
  const strokeWidth = 20;
  const circumference = 2 * Math.PI * radius;

  return (
    <div className="text-center bg-slate-50 rounded-lg p-6 border border-slate-200">
      <h4 className="text-sm font-semibold text-slate-900 mb-4">{title}</h4>
      <div className="relative inline-block">
        <svg width={size} height={size} className="transform -rotate-90">
          <circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            fill="none"
            stroke="rgba(255,255,255,0.2)"
            strokeWidth={strokeWidth}
          />
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const segmentLength = (percentage / 100) * circumference;
            const offset = -((cumulativePercentage / 100) * circumference);

            cumulativePercentage += percentage;

            return (
              <circle
                key={index}
                cx={size / 2}
                cy={size / 2}
                r={radius}
                fill="none"
                stroke={item.color}
                strokeWidth={strokeWidth}
                strokeDasharray={`${segmentLength} ${circumference}`}
                strokeDashoffset={offset}
                className="transition-all duration-500"
                style={{
                  filter: 'drop-shadow(0 0 2px rgba(0,0,0,0.3))'
                }}
              />
            );
          })}
        </svg>
        {centerText && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-lg font-bold text-slate-900">{centerText}</div>
              <div className="text-xs text-slate-600">Total</div>
            </div>
          </div>
        )}
      </div>
      <div className="mt-3 space-y-1">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between text-xs">
            <div className="flex items-center">
              <div 
                className="w-3 h-3 rounded-full mr-2" 
                style={{ backgroundColor: item.color }}
              />
              <span className="text-gray-300">{item.label}</span>
            </div>
            <span className="text-white font-medium">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default DonutChart;