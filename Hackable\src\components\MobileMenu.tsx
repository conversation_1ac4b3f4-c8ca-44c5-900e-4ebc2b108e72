import React, { useState, useEffect } from 'react';
import { Menu, X, Home, Info, Shield, Share2, Moon, Sun, ExternalLink } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';

interface MobileMenuProps {
  currentPage: string;
  onNavigate: (page: string) => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ currentPage, onNavigate }) => {
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isOpen && !target.closest('.mobile-menu-container')) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen]);

  // Prevent body scroll when menu is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const menuItems = [
    {
      id: 'home',
      label: 'Home',
      icon: Home,
      description: 'Check your hackability'
    },
    {
      id: 'about',
      label: 'About',
      icon: Info,
      description: 'Learn about CyberDucky'
    },
    {
      id: 'security-tips',
      label: 'Security Tips',
      icon: Shield,
      description: 'Cybersecurity best practices'
    }
  ];

  const handleItemClick = (itemId: string) => {
    onNavigate(itemId);
    setIsOpen(false);
    window.location.hash = `#${itemId}`;
  };

  const handleShare = async () => {
    setIsOpen(false);
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Hackable - Cybersecurity Assessment',
          text: 'Check how hackable you are with this comprehensive cybersecurity assessment!',
          url: window.location.href,
        });
      } catch (error) {
        console.log('Share cancelled or failed:', error);
      }
    } else {
      // Fallback for browsers without Web Share API
      try {
        await navigator.clipboard.writeText(window.location.href);
        alert('Link copied to clipboard!');
      } catch (error) {
        console.error('Failed to copy link:', error);
      }
    }
  };

  return (
    <div className="mobile-menu-container md:hidden">
      {/* Hamburger Menu Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`fixed top-4 left-4 z-50 p-3 rounded-lg transition-all duration-200 ${
          isDarkMode
            ? 'bg-slate-800 hover:bg-slate-700 text-white'
            : 'bg-white hover:bg-slate-50 text-slate-900'
        } shadow-lg border ${
          isDarkMode ? 'border-slate-700' : 'border-slate-200'
        }`}
        aria-label="Toggle menu"
      >
        {isOpen ? (
          <X className="h-6 w-6" />
        ) : (
          <Menu className="h-6 w-6" />
        )}
      </button>

      {/* Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity duration-200"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Menu Panel */}
      <div className={`fixed top-0 left-0 h-full w-80 max-w-[85vw] z-50 transform transition-transform duration-300 ease-in-out ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } ${
        isDarkMode 
          ? 'bg-slate-900 border-slate-700' 
          : 'bg-white border-slate-200'
      } border-r shadow-2xl`}>
        
        {/* Header */}
        <div className={`p-6 border-b ${
          isDarkMode ? 'border-slate-700' : 'border-slate-200'
        }`}>
          <div className="flex items-center">
            <img src="/CyberDuckyLogo.png" alt="Hackable" className="w-10 h-10 mr-3" />
            <div>
              <h2 className={`text-xl font-bold transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>
                Hackable
              </h2>
              <p className={`text-sm transition-colors duration-200 ${
                isDarkMode ? 'text-slate-400' : 'text-slate-500'
              }`}>
                Cybersecurity Assessment
              </p>
            </div>
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="p-4">
          <div className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.id;
              
              return (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item.id)}
                  className={`w-full flex items-center p-4 rounded-lg transition-all duration-200 text-left ${
                    isActive
                      ? isDarkMode
                        ? 'bg-blue-600 text-white'
                        : 'bg-blue-500 text-white'
                      : isDarkMode
                        ? 'text-slate-300 hover:bg-slate-800 hover:text-white'
                        : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
                  }`}
                >
                  <Icon className={`h-5 w-5 mr-4 ${isActive ? 'text-white' : 'text-blue-500'}`} />
                  <div>
                    <div className="font-medium">{item.label}</div>
                    <div className={`text-xs ${
                      isActive 
                        ? 'text-blue-100' 
                        : isDarkMode 
                          ? 'text-slate-500' 
                          : 'text-slate-500'
                    }`}>
                      {item.description}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>

          {/* Divider */}
          <div className={`my-6 border-t ${
            isDarkMode ? 'border-slate-700' : 'border-slate-200'
          }`} />

          {/* Action Items */}
          <div className="space-y-2">
            {/* Share Button */}
            <button
              onClick={handleShare}
              className={`w-full flex items-center p-4 rounded-lg transition-all duration-200 text-left ${
                isDarkMode
                  ? 'text-slate-300 hover:bg-slate-800 hover:text-white'
                  : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
              }`}
            >
              <Share2 className="h-5 w-5 mr-4 text-green-500" />
              <div>
                <div className="font-medium">Share App</div>
                <div className={`text-xs ${
                  isDarkMode ? 'text-slate-500' : 'text-slate-500'
                }`}>
                  Tell others about Hackable
                </div>
              </div>
            </button>

            {/* Dark Mode Toggle */}
            <button
              onClick={toggleDarkMode}
              className={`w-full flex items-center p-4 rounded-lg transition-all duration-200 text-left ${
                isDarkMode
                  ? 'text-slate-300 hover:bg-slate-800 hover:text-white'
                  : 'text-slate-700 hover:bg-slate-100 hover:text-slate-900'
              }`}
            >
              {isDarkMode ? (
                <Sun className="h-5 w-5 mr-4 text-yellow-500" />
              ) : (
                <Moon className="h-5 w-5 mr-4 text-slate-600" />
              )}
              <div>
                <div className="font-medium">
                  {isDarkMode ? 'Light Mode' : 'Dark Mode'}
                </div>
                <div className={`text-xs ${
                  isDarkMode ? 'text-slate-500' : 'text-slate-500'
                }`}>
                  Switch theme
                </div>
              </div>
            </button>
          </div>
        </nav>

        {/* Footer */}
        <div className={`absolute bottom-0 left-0 right-0 p-4 border-t ${
          isDarkMode ? 'border-slate-700' : 'border-slate-200'
        }`}>
          <a
            href="https://cyber-ducky.com/links"
            target="_blank"
            rel="noopener noreferrer"
            className={`w-full flex items-center justify-center p-3 rounded-lg transition-all duration-200 ${
              isDarkMode
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
            onClick={() => setIsOpen(false)}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Get Updates
          </a>
        </div>
      </div>
    </div>
  );
};

export default MobileMenu;
