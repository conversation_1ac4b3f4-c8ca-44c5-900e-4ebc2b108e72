import React, { useState } from 'react';
import Hackability<PERSON>hecker from './components/HackabilityChecker';
import About from './components/About';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  // Simple routing based on hash
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.slice(1);
      setCurrentPage(hash || 'home');
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check initial hash

    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  const renderPage = () => {
    switch (currentPage) {
      case 'about':
        return <About />;
      case 'home':
      default:
        return <HackabilityChecker />;
    }
  };

  return renderPage();
}

export default App;