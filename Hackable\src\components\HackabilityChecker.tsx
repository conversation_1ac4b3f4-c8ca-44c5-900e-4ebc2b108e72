import React, { useState, useEffect } from 'react';
import { Alert<PERSON>riangle, ExternalLink, Check, X, User, Mail, MapPin, Loader, Trash2, Users, Shield, Database, Calendar, PieChart, Coffee, Moon, Sun, Lock, Key, Share2, Instagram, Video, Download, Smartphone } from 'lucide-react';
import checklistData from '../data/checklist.json';
import Donut<PERSON>hart from './DonutChart';
import Terms from './Terms';
import SocialShare from './SocialShare';
import PWAInstallPrompt from './PWAInstallPrompt';
import TikTokEmbed from './TikTokEmbed';
import LazyLoad from './LazyLoad';
import MobileMenu from './MobileMenu';
import { useTheme } from '../contexts/ThemeContext';
import { useDeviceDetection, shouldAutoplayVideo, getOptimalChunkSize } from '../hooks/useDeviceDetection';
import {
  validateUserInfo,
  isValidUserInfo,
  getSanitizedUserInfo,
  getAllValidationErrors,
  RateLimiter
} from '../utils/validation';
// import { WebScrapingService } from '../services/webScraper'; // Disabled for now

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  urlTemplate: string;
  weight: number;
  requiresEmail?: boolean;
  requiresName?: boolean;
  requiresLocation?: boolean;
  requiresManual?: boolean;
  requiresUsername?: boolean;
}

interface UserInfo {
  firstName: string;
  lastName: string;
  email: string;
  city: string;
  state: string;
  username: string;
}

interface BreachData {
  Name: string;
  Title: string;
  Domain: string;
  BreachDate: string;
  AddedDate: string;
  ModifiedDate: string;
  PwnCount: number;
  Description: string;
  LogoPath: string;
  DataClasses: string[];
  IsVerified: boolean;
  IsFabricated: boolean;
  IsSensitive: boolean;
  IsRetired: boolean;
  IsSpamList: boolean;
  IsMalware: boolean;
  IsSubscriptionFree: boolean;
}

interface XposedData {
  BreachMetrics?: {
    passwords_strength?: Array<{
      EasyToCrack: number;
      PlainText: number;
      StrongHash: number;
      Unknown: number;
    }>;
    risk?: Array<{
      risk_label: string;
      risk_score: number;
    }>;
  };
  ExposedBreaches?: {
    breaches_details?: Array<{
      breach: string;
      details: string;
      domain: string;
      industry: string;
      logo: string;
      password_risk: string;
      xposed_date: string;
      xposed_records: number;
      xposed_data: string;
      verified: string;
    }>;
  };
  // Legacy format support
  passwords_strength?: Array<{
    EasyToCrack: number;
    PlainText: number;
    StrongHash: number;
    Unknown: number;
  }>;
  risk?: Array<{
    risk_label: string;
    risk_score: number;
  }>;
}

interface PeopleSearchResult {
  name: string;
  age?: string;
  addresses: string[];
  phoneNumbers: string[];
  relatives: string[];
  associates: string[];
  previousAddresses: string[];
  foundRecords: number;
}

interface BreachReport {
  hibpBreaches: BreachData[];
  xposedData: XposedData | null;
  peopleSearchData: PeopleSearchResult | null;
  totalBreaches: number;
  totalRecords: number;
  isLoading: boolean;
  error: string | null;
  peopleSearchLoading: boolean;
  peopleSearchError: string | null;
}

const HackabilityChecker: React.FC = () => {
  const [userInfo, setUserInfo] = useState<UserInfo>({
    firstName: '',
    lastName: '',
    email: '',
    city: '',
    state: '',
    username: ''
  });
  const [checkedIds, setCheckedIds] = useState<string[]>([]);
  const [items] = useState<ChecklistItem[]>(checklistData);
  const [formValid, setFormValid] = useState(false);
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showTerms, setShowTerms] = useState(false);
  const [searchInitiated, setSearchInitiated] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const [rateLimiter] = useState(() => new RateLimiter(3, 60000)); // 3 attempts per minute
  const { isDarkMode, toggleDarkMode } = useTheme();
  const deviceInfo = useDeviceDetection();
  const [breachReport, setBreachReport] = useState<BreachReport>({
    hibpBreaches: [],
    xposedData: null,
    peopleSearchData: null,
    totalBreaches: 0,
    totalRecords: 0,
    isLoading: false,
    error: null,
    peopleSearchLoading: false,
    peopleSearchError: null
  });

  // Validate form with security validation
  useEffect(() => {
    const validation = validateUserInfo(userInfo);
    const isValid = isValidUserInfo(validation);
    const errors = getAllValidationErrors(validation);

    setFormValid(isValid);
    setValidationErrors(errors);
  }, [userInfo]);

  // Reset cache and clear fields on component mount
  useEffect(() => {
    // Clear localStorage on every refresh
    localStorage.removeItem('hackability-state');
    // Reset all fields to empty
    setUserInfo({
      firstName: '',
      lastName: '',
      email: '',
      city: '',
      state: '',
      username: ''
    });
    setCheckedIds([]);
    setSearchInitiated(false);
    setTermsAccepted(false);
    setBreachReport({
      hibpBreaches: [],
      xposedData: null,
      peopleSearchData: null,
      totalBreaches: 0,
      totalRecords: 0,
      isLoading: false,
      error: null,
      peopleSearchLoading: false,
      peopleSearchError: null
    });
  }, []);

  // Automatically check breaches when email is entered and valid - DISABLED
  // useEffect(() => {
  //   if (userInfo.email && userInfo.email.includes('@') && userInfo.email.trim().length > 5) {
  //     // Add a small delay to avoid rapid API calls
  //     const timeoutId = setTimeout(() => {
  //       checkXposedData(userInfo.email);
  //     }, 1000);
  //
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [userInfo.email]);

  // Automatically check people search when form is valid - DISABLED
  // useEffect(() => {
  //   if (formValid) {
  //     const timeoutId = setTimeout(() => {
  //       checkPeopleSearch();
  //     }, 1500);
  //
  //     return () => clearTimeout(timeoutId);
  //   }
  // }, [formValid, userInfo.firstName, userInfo.lastName, userInfo.city, userInfo.state]);

  // Find Me function - triggers all searches when button is clicked
  const handleFindMe = async () => {
    if (!termsAccepted) {
      alert('Please accept the Terms and Conditions to proceed.');
      return;
    }

    // Rate limiting check
    const clientId = userInfo.email || 'anonymous';
    if (!rateLimiter.isAllowed(clientId)) {
      const remainingTime = Math.ceil(rateLimiter.getRemainingTime(clientId) / 1000);
      alert(`Too many requests. Please wait ${remainingTime} seconds before trying again.`);
      return;
    }

    // Validate and sanitize input - show errors when button is pressed
    const validation = validateUserInfo(userInfo);
    if (!isValidUserInfo(validation)) {
      const errors = getAllValidationErrors(validation);
      setValidationErrors(errors);
      setShowValidationErrors(true);
      return;
    }

    // Clear validation errors if form is valid
    setShowValidationErrors(false);
    setValidationErrors([]);

    // Get sanitized data
    const sanitizedUserInfo = getSanitizedUserInfo(validation);

    // Set search initiated flag to show the breach report section
    setSearchInitiated(true);

    if (sanitizedUserInfo.email) {
      await checkXposedData(sanitizedUserInfo.email);
    }
  };

  // People search function - DISABLED but kept for future use
  // const checkPeopleSearch = async () => {
  //   setBreachReport(prev => ({ ...prev, peopleSearchLoading: true, peopleSearchError: null }));
  //
  //   try {
  //     const result = await WebScrapingService.scrapeTruePeopleSearch(
  //       userInfo.firstName,
  //       userInfo.lastName,
  //       userInfo.city,
  //       userInfo.state
  //     );

  //     if (result.success && result.data) {
  //       setBreachReport(prev => ({
  //         ...prev,
  //         peopleSearchData: result.data,
  //         peopleSearchLoading: false,
  //         peopleSearchError: null
  //       }));
  //     } else {
  //       throw new Error(result.error || 'Failed to scrape people search data');
  //     }
  //   } catch (error) {
  //     console.error('People search error:', error);
  //
  //     let errorMessage = 'Unable to check people search database at this time.';
  //
  //     if (error instanceof Error) {
  //       if (error.message.includes('CAPTCHA_DETECTED')) {
  //         errorMessage = '🤖 CAPTCHA detected! The website is blocking automated requests. This is common for people search sites. You can manually visit the TruePeopleSearch link below to check your information.';
  //       } else if (error.message.includes('blocked') || error.message.includes('403')) {
  //         errorMessage = '🚫 Access blocked by the website. Try again later or use the manual link below.';
  //       } else if (error.message.includes('network') || error.message.includes('fetch')) {
  //         errorMessage = '🌐 Network error. Please check your internet connection and try again.';
  //       }
  //     }
  //
  //     setBreachReport(prev => ({
  //       ...prev,
  //       peopleSearchLoading: false,
  //       peopleSearchError: errorMessage
  //     }));
  //   }
  // };

  const checkXposedData = async (email: string) => {
    setBreachReport(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      let xposedData: XposedData | null = null;

      // Check XposedOrNot API
      try {
        const xposedUrl = `https://cors-header-proxy.juansoberanes64.workers.dev/?url=${encodeURIComponent(`https://api.xposedornot.com/v1/breach-analytics?email=${email}`)}`;
        const xposedResponse = await fetch(xposedUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'User-Agent': 'HackabilityChecker'
          }
        });

        if (xposedResponse.ok) {
          const responseText = await xposedResponse.text();
          if (responseText.trim()) {
            try {
              xposedData = JSON.parse(responseText);
            } catch (parseError) {
              console.warn('Failed to parse XposedOrNot response:', parseError);
            }
          }
        }
      } catch (xposedError) {
        console.warn('XposedOrNot API error:', xposedError);
      }

      // Parse the API response
      const breachDetails = xposedData?.ExposedBreaches?.breaches_details || [];
      const totalBreaches = breachDetails.length;
      const totalRecords = breachDetails.reduce(
        (sum, breach) => sum + (breach.xposed_records || 0),
        0
      );

      setBreachReport(prev => ({
        ...prev,
        xposedData,
        totalBreaches,
        totalRecords,
        isLoading: false,
        error: null
      }));
    } catch (error) {
      console.error('XposedOrNot check error:', error);
      setBreachReport(prev => ({
        ...prev,
        isLoading: false,
        error: 'Unable to check XposedOrNot database at this time. This may be due to network issues or API limitations.'
      }));
    }
  };

  const handleUserInfoChange = (field: keyof UserInfo, value: string) => {
    setUserInfo(prev => ({ ...prev, [field]: value }));
    // Reset checked items when user info changes significantly
    if (field === 'email' && value !== userInfo.email) {
      setCheckedIds([]);
      setBreachReport({
        hibpBreaches: [],
        xposedData: null,
        peopleSearchData: null,
        totalBreaches: 0,
        totalRecords: 0,
        isLoading: false,
        error: null,
        peopleSearchLoading: false,
        peopleSearchError: null
      });
    }
  };

  const toggleChecked = (itemId: string) => {
    setCheckedIds(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const generateUrl = (item: ChecklistItem) => {
    let url = item.urlTemplate;
    
    if (item.requiresEmail) {
      url = url.replace('{{EMAIL}}', encodeURIComponent(userInfo.email));
    }
    
    if (item.requiresName) {
      const fullName = `${userInfo.firstName} ${userInfo.lastName}`;
      url = url.replace('{{FULL_NAME}}', encodeURIComponent(fullName));
    }
    
    if (item.requiresLocation) {
      url = url.replace('{{CITY}}', encodeURIComponent(userInfo.city));
      url = url.replace('{{STATE}}', encodeURIComponent(userInfo.state));
    }
    
    if (item.requiresUsername) {
      url = url.replace('{{USERNAME}}', encodeURIComponent(userInfo.username));
    }

    // Handle dynamic username replacement for whatsmyname.app
    url = url.replace('{username}', encodeURIComponent(userInfo.username));

    return url;
  };

  const openCheck = (item: ChecklistItem) => {
    if (!formValid) {
      alert('Please fill in all required fields first');
      return;
    }
    
    const url = generateUrl(item);
    window.open(url, '_blank');
  };

  // Calculate score
  const totalItems = items.length;
  const cleanedCount = checkedIds.length;
  const score = Math.round(100 - (cleanedCount / totalItems) * 100);

  // Score color and status
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-red-600';
    if (score >= 60) return 'text-orange-600';
    if (score >= 40) return 'text-yellow-600';
    if (score >= 20) return 'text-green-600';
    return 'text-green-700';
  };

  const getScoreStatus = (score: number) => {
    if (score >= 80) return 'Highly Hackable';
    if (score >= 60) return 'Somewhat Hackable';
    if (score >= 40) return 'Moderately Secure';
    if (score >= 20) return 'Quite Secure';
    return 'Very Secure';
  };

  const progressPercentage = (cleanedCount / totalItems) * 100;

  return (
    <div className={`min-h-screen transition-colors duration-200 ${isDarkMode ? 'bg-slate-900' : 'bg-slate-50'}`}>
      {/* Mobile Menu */}
      <MobileMenu currentPage="home" onNavigate={(page) => window.location.hash = `#${page}`} />

      {/* Navbar */}
      <nav className={`border-b px-6 py-4 shadow-sm transition-colors duration-200 ${
        isDarkMode
          ? 'bg-slate-800 border-slate-700'
          : 'bg-white border-slate-200'
      }`}>
        <div className="container mx-auto max-w-6xl flex items-center justify-between">
          <div className="flex items-center space-x-8">
            {/* Logo - centered on mobile, left-aligned on desktop */}
            <div className="flex items-center md:ml-0 ml-16">
              <img src="/CyberDuckyLogo.png" alt="Hackable Logo" className="w-8 h-8 object-contain mr-3" />
              <span className={`font-semibold text-xl tracking-tight transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>Hackable</span>
            </div>
            {/* Desktop Navigation - hidden on mobile */}
            <div className="hidden md:flex space-x-8">
              <a href="#home" className={`font-medium hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-white' : 'text-slate-900'
              }`}>
                Home
              </a>
              <a href="#about" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                About
              </a>
              <a href="#security-tips" className={`hover:text-blue-500 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-600'
              }`}>
                Security Tips
              </a>
            </div>
          </div>
          {/* Desktop Actions - hidden on mobile */}
          <div className="hidden md:flex items-center space-x-4">
            <button
              onClick={toggleDarkMode}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 hover:bg-slate-600 text-yellow-400'
                  : 'bg-slate-100 hover:bg-slate-200 text-slate-600'
              }`}
              aria-label="Toggle dark mode"
            >
              {isDarkMode ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </button>
            <a
              href="https://cyber-ducky.com/links"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2.5 rounded-lg transition-colors duration-200 font-medium shadow-sm"
            >
              Need Help?
            </a>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-6 py-16 max-w-5xl">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-6 tracking-tight transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            How Hackable Am I?
          </h1>
          <p className={`text-xl md:text-2xl max-w-3xl mx-auto leading-relaxed font-light transition-colors duration-200 ${
            isDarkMode ? 'text-slate-300' : 'text-slate-600'
          }`}>
“Hey there! I’m Juan—just a regular guy who got curious about how much of our lives are floating around online. I built How Hackable Am I? to give you a quick, fun peek at what info you’ve left behind—no tech degree needed. Think of it as a friendly mirror for your digital self. Ready to see how you stack up?”
          </p>
          <div className="mt-8 flex justify-center">
            <div className={`border rounded-lg px-6 py-3 inline-flex items-center transition-colors duration-200 ${
              isDarkMode
                ? 'bg-blue-900/20 border-blue-800 text-blue-300'
                : 'bg-blue-50 border-blue-200 text-blue-800'
            }`}>
              <Shield className="h-5 w-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium">Your data stays private - all checks performed securely in your browser</span>
            </div>
          </div>
        </div>

        {/* User Information Form */}
        <div className={`rounded-xl p-8 mb-12 border shadow-sm transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <h2 className={`text-2xl font-semibold mb-8 flex items-center transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            <User className="h-6 w-6 mr-3 text-blue-500" />
            Your Information
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label className={`block text-sm font-medium mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                First Name *
              </label>
              <input
                type="text"
                value={userInfo.firstName}
                onChange={(e) => handleUserInfoChange('firstName', e.target.value)}
                placeholder="Enter your first name"
                autoFocus
                className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                Last Name *
              </label>
              <input
                type="text"
                value={userInfo.lastName}
                onChange={(e) => handleUserInfoChange('lastName', e.target.value)}
                placeholder="Enter your last name"
                className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
              />
            </div>
          </div>

          <div className="mb-6">
            <label className={`block text-sm font-medium mb-3 flex items-center transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-700'
            }`}>
              <Mail className="h-4 w-4 mr-2 text-blue-500" />
              Email Address *
            </label>
            <input
              type="email"
              value={userInfo.email}
              onChange={(e) => handleUserInfoChange('email', e.target.value)}
              placeholder="<EMAIL>"
              className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                  : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
              }`}
            />
          </div>

          <div className="mb-6">
            <label className={`block text-sm font-medium mb-3 transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-700'
            }`}>
              Username/Handle *
            </label>
            <input
              type="text"
              value={userInfo.username}
              onChange={(e) => handleUserInfoChange('username', e.target.value)}
              placeholder="your_username"
              className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                isDarkMode
                  ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                  : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
              }`}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className={`block text-sm font-medium mb-3 flex items-center transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                <MapPin className="h-4 w-4 mr-2 text-blue-500" />
                City *
              </label>
              <input
                type="text"
                value={userInfo.city}
                onChange={(e) => handleUserInfoChange('city', e.target.value)}
                placeholder="Enter your city"
                className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
              />
            </div>

            <div>
              <label className={`block text-sm font-medium mb-3 transition-colors duration-200 ${
                isDarkMode ? 'text-slate-300' : 'text-slate-700'
              }`}>
                State *
              </label>
              <input
                type="text"
                value={userInfo.state}
                onChange={(e) => handleUserInfoChange('state', e.target.value)}
                placeholder="Enter your state"
                className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 ${
                  isDarkMode
                    ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400'
                    : 'bg-white border-slate-300 text-slate-900 placeholder-slate-400'
                }`}
              />
            </div>
          </div>

          {showValidationErrors && validationErrors.length > 0 && (
            <div className={`mt-4 p-4 rounded-lg border ${
              isDarkMode
                ? 'bg-red-900/20 border-red-800 text-red-300'
                : 'bg-red-50 border-red-200 text-red-700'
            }`}>
              <h4 className="font-medium mb-2">Please fix the following errors:</h4>
              <ul className="list-disc list-inside space-y-1 text-sm">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          )}

          {!formValid && !showValidationErrors && (
            <p className={`text-sm mt-4 p-3 rounded-lg border ${
              isDarkMode
                ? 'text-amber-300 bg-amber-900/20 border-amber-800'
                : 'text-amber-600 bg-amber-50 border-amber-200'
            }`}>
              Please complete all required fields to begin your privacy assessment.
            </p>
          )}

          {/* Find Me Button and Terms */}
          {formValid && (
            <div className="mt-8 space-y-6">
              <div className="flex items-start space-x-3">
                <input
                  type="checkbox"
                  id="terms"
                  checked={termsAccepted}
                  onChange={(e) => setTermsAccepted(e.target.checked)}
                  className={`w-4 h-4 text-blue-600 rounded focus:ring-blue-500 focus:ring-2 mt-0.5 transition-colors duration-200 ${
                    isDarkMode
                      ? 'bg-slate-700 border-slate-600'
                      : 'bg-white border-slate-300'
                  }`}
                />
                <label htmlFor="terms" className={`text-sm leading-relaxed transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                }`}>
                  I understand that my information will be checked against public databases and agree to the{' '}
                  <button
                    onClick={() => setShowTerms(true)}
                    className={`underline font-medium transition-colors duration-200 ${
                      isDarkMode
                        ? 'text-blue-400 hover:text-blue-300'
                        : 'text-blue-600 hover:text-blue-700'
                    }`}
                  >
                    Terms and Conditions
                  </button>
                </label>
              </div>

              <div className="flex justify-center pt-4">
                <button
                  onClick={handleFindMe}
                  disabled={!termsAccepted || breachReport.isLoading}
                  className={`px-8 py-4 rounded-lg font-semibold text-lg transition-all duration-200 ${
                    termsAccepted && !breachReport.isLoading
                      ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                      : 'bg-slate-300 text-slate-500 cursor-not-allowed'
                  }`}
                >
                  {breachReport.isLoading ? (
                    <div className="flex items-center">
                      <Loader className="h-5 w-5 animate-spin mr-2" />
                      Analyzing your digital footprint...
                    </div>
                  ) : (
                    'Start Privacy Assessment'
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        {/* People Search Report Section - DISABLED */}
        {/* The web scraping functionality is kept in the code but UI is hidden due to CAPTCHA issues */}
        {/* To re-enable, uncomment the section below and the useEffect for automatic triggering */}

        {/* Breach Report Section */}
        {searchInitiated && userInfo.email && userInfo.email.includes('@') && (
          <div className={`rounded-xl p-8 mb-12 border shadow-sm transition-colors duration-200 ${
            isDarkMode
              ? 'bg-slate-800 border-slate-700'
              : 'bg-white border-slate-200'
          }`}>
            <h2 className={`text-2xl font-semibold mb-8 flex items-center transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}>
              <Database className="h-6 w-6 mr-3 text-blue-500" />
              Privacy Assessment Results
            </h2>

            {breachReport.isLoading && (
              <div className="flex items-center justify-center py-12">
                <Loader className="h-8 w-8 animate-spin text-blue-500 mr-3" />
                <span className={`text-lg transition-colors duration-200 ${
                  isDarkMode ? 'text-slate-300' : 'text-slate-700'
                }`}>Analyzing your digital footprint...</span>
              </div>
            )}

            {breachReport.error && (
              <div className={`rounded-lg p-4 mb-4 transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-red-900/30 border border-red-800'
                  : 'bg-red-50 border border-red-200'
              }`}>
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                  <h4 className={`font-semibold transition-colors duration-200 ${
                    isDarkMode ? 'text-red-300' : 'text-red-700'
                  }`}>Connection Issue</h4>
                </div>
                <p className={`text-sm mb-3 transition-colors duration-200 ${
                  isDarkMode ? 'text-red-200' : 'text-red-600'
                }`}>{breachReport.error}</p>
                <button
                  onClick={() => checkXposedData(userInfo.email)}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg text-sm transition-colors duration-200"
                >
                  Try Again
                </button>
              </div>
            )}

            {!breachReport.isLoading && !breachReport.error && breachReport.totalBreaches === 0 && (
              <div className={`rounded-lg p-6 text-center transition-colors duration-200 ${
                isDarkMode
                  ? 'bg-green-900/30 border border-green-800'
                  : 'bg-green-50 border border-green-200'
              }`}>
                <Shield className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <h3 className={`text-2xl font-bold mb-2 transition-colors duration-200 ${
                  isDarkMode ? 'text-green-300' : 'text-green-700'
                }`}>Great News!</h3>
                <p className={`text-lg transition-colors duration-200 ${
                  isDarkMode ? 'text-green-200' : 'text-green-600'
                }`}>
                  No breaches found for your email address in XposedOrNot database. You are less hackable! 🎉
                </p>
              </div>
            )}

            {!breachReport.isLoading && breachReport.xposedData && (
              <div>
                {breachReport.totalBreaches > 0 && (
                  <div className={`rounded-lg p-4 mb-6 transition-colors duration-200 ${
                    isDarkMode
                      ? 'bg-red-900/30 border border-red-800'
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    <div className="flex items-center mb-3">
                      <AlertTriangle className="h-6 w-6 text-red-500 mr-2" />
                      <h3 className={`text-xl font-bold transition-colors duration-200 ${
                        isDarkMode ? 'text-red-300' : 'text-red-700'
                      }`}>Security Alert</h3>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                      <div className={`rounded-lg p-3 transition-colors duration-200 ${
                        isDarkMode ? 'bg-red-800/50' : 'bg-red-100'
                      }`}>
                        <div className={`text-2xl font-bold transition-colors duration-200 ${
                          isDarkMode ? 'text-red-200' : 'text-red-700'
                        }`}>{breachReport.totalBreaches}</div>
                        <div className={`text-sm transition-colors duration-200 ${
                          isDarkMode ? 'text-red-300' : 'text-red-600'
                        }`}>Total Breaches</div>
                      </div>
                      <div className={`rounded-lg p-3 transition-colors duration-200 ${
                        isDarkMode ? 'bg-red-800/50' : 'bg-red-100'
                      }`}>
                        <div className={`text-2xl font-bold transition-colors duration-200 ${
                          isDarkMode ? 'text-red-200' : 'text-red-700'
                        }`}>{breachReport.totalRecords.toLocaleString()}</div>
                        <div className={`text-sm transition-colors duration-200 ${
                          isDarkMode ? 'text-red-300' : 'text-red-600'
                        }`}>Records Exposed</div>
                      </div>
                      <div className={`rounded-lg p-3 transition-colors duration-200 ${
                        isDarkMode ? 'bg-red-800/50' : 'bg-red-100'
                      }`}>
                        <div className={`text-2xl font-bold transition-colors duration-200 ${
                          isDarkMode ? 'text-red-200' : 'text-red-700'
                        }`}>
                          {breachReport.totalBreaches}
                        </div>
                        <div className={`text-sm transition-colors duration-200 ${
                          isDarkMode ? 'text-red-300' : 'text-red-600'
                        }`}>Companies Affected</div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Password Strength and Risk Analysis */}
                {breachReport.xposedData && (
                  (breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength) ||
                  (breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)
                ) && (
                  <div className="mb-6">
                    <h4 className={`text-lg font-semibold mb-4 flex items-center transition-colors duration-200 ${
                      isDarkMode ? 'text-white' : 'text-slate-900'
                    }`}>
                      <PieChart className="h-5 w-5 mr-2 text-blue-500" />
                      Security Analysis
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Password Strength Donut */}
                      {((breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength)?.[0]) && (
                        <div className={`rounded-lg p-4 transition-colors duration-200 ${
                          isDarkMode ? 'bg-slate-700' : 'bg-slate-50'
                        }`}>
                          {(() => {
                            const passwordData = (breachReport.xposedData.BreachMetrics?.passwords_strength || breachReport.xposedData.passwords_strength)?.[0];
                            return (
                          <DonutChart
                            title="Password Strength Distribution"
                            centerText={`${((passwordData?.EasyToCrack || 0) + (passwordData?.PlainText || 0) + (passwordData?.StrongHash || 0) + (passwordData?.Unknown || 0)).toLocaleString()}`}
                            data={[
                              {
                                label: 'Easy to Crack',
                                value: passwordData?.EasyToCrack || 0,
                                color: '#dc2626' // Bright red for critical risk
                              },
                              {
                                label: 'Plain Text',
                                value: passwordData?.PlainText || 0,
                                color: '#ea580c' // Orange-red for high risk
                              },
                              {
                                label: 'Weak Hash',
                                value: Math.floor((passwordData?.EasyToCrack || 0) * 0.3), // Derived category for better visualization
                                color: '#f59e0b' // Amber for medium-high risk
                              },
                              {
                                label: 'Medium Hash',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.4), // Derived category
                                color: '#eab308' // Yellow for medium risk
                              },
                              {
                                label: 'Strong Hash',
                                value: passwordData?.StrongHash || 0,
                                color: '#65a30d' // Lime green for good security
                              },
                              {
                                label: 'Very Strong',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.2), // Derived category
                                color: '#16a34a' // Green for very good security
                              },
                              {
                                label: 'Encrypted',
                                value: Math.floor((passwordData?.StrongHash || 0) * 0.1), // Derived category
                                color: '#059669' // Emerald for excellent security
                              },
                              {
                                label: 'Unknown',
                                value: passwordData?.Unknown || 0,
                                color: '#6b7280' // Gray for unknown
                              }
                            ].filter(item => item.value > 0)} // Only show categories with data
                          />
                            );
                          })()}
                        </div>
                      )}
                      
                      {/* Risk Score */}
                      {((breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)?.[0]) && (
                        <div className={`rounded-lg p-4 transition-colors duration-200 ${
                          isDarkMode ? 'bg-slate-700' : 'bg-slate-50'
                        }`}>
                          {(() => {
                            const riskData = (breachReport.xposedData.BreachMetrics?.risk || breachReport.xposedData.risk)?.[0];
                            return (
                          <div className="text-center">
                            <h4 className={`text-sm font-semibold mb-3 transition-colors duration-200 ${
                              isDarkMode ? 'text-white' : 'text-slate-900'
                            }`}>Risk Assessment</h4>
                            <div className="relative inline-block">
                              <svg width="120" height="120" className="transform -rotate-90">
                                <circle
                                  cx="60"
                                  cy="60"
                                  r="50"
                                  fill="none"
                                  stroke="rgba(255,255,255,0.1)"
                                  strokeWidth="20"
                                />
                                <circle
                                  cx="60"
                                  cy="60"
                                  r="50"
                                  fill="none"
                                  stroke={
                                    (riskData?.risk_score || 0) >= 70 ? '#ef4444' :
                                    (riskData?.risk_score || 0) >= 40 ? '#f59e0b' : '#22c55e'
                                  }
                                  strokeWidth="20"
                                  strokeDasharray={`${((riskData?.risk_score || 0) / 100) * (2 * Math.PI * 50)} ${2 * Math.PI * 50}`}
                                  className="transition-all duration-500"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <div className="text-center">
                                  <div className={`text-lg font-bold transition-colors duration-200 ${
                                    isDarkMode ? 'text-white' : 'text-slate-900'
                                  }`}>{riskData?.risk_score || 0}</div>
                                  <div className={`text-xs transition-colors duration-200 ${
                                    isDarkMode ? 'text-slate-400' : 'text-slate-600'
                                  }`}>Risk Score</div>
                                </div>
                              </div>
                            </div>
                            <div className="mt-3">
                              <div className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${
                                (riskData?.risk_score || 0) >= 70 ? 'bg-red-800/50 text-red-200' :
                                (riskData?.risk_score || 0) >= 40 ? 'bg-yellow-800/50 text-yellow-200' : 
                                'bg-green-800/50 text-green-200'
                              }`}>
                                {riskData?.risk_label || 'Unknown'} Risk
                              </div>
                            </div>
                          </div>
                            );
                          })()}
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* XposedOrNot Breaches */}
                {breachReport.xposedData?.ExposedBreaches?.breaches_details && breachReport.xposedData.ExposedBreaches.breaches_details.length > 0 && (
                  <div className="mb-6">
                    <h4 className={`text-lg font-semibold mb-4 flex items-center transition-colors duration-200 ${
                      isDarkMode ? 'text-white' : 'text-slate-900'
                    }`}>
                      <img src="/CyberDuckyLogo.png" alt="XON" className="w-5 h-5 mr-2" />
                      XposedOrNot Results ({breachReport.xposedData.ExposedBreaches.breaches_details.length} breaches)
                    </h4>
                    <div className="space-y-3">
                      {breachReport.xposedData.ExposedBreaches.breaches_details.map((breach, index) => (
                        <div key={index} className={`rounded-lg p-4 border border-orange-500/30 transition-colors duration-200 ${
                          isDarkMode ? 'bg-slate-700' : 'bg-orange-50'
                        }`}>
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex items-center">
                              <div className="w-8 h-8 bg-orange-600 rounded-full flex items-center justify-center mr-3">
                                <Database className="h-4 w-4 text-white" />
                              </div>
                              <div>
                                <h5 className={`font-semibold transition-colors duration-200 ${
                                  isDarkMode ? 'text-white' : 'text-slate-900'
                                }`}>{breach.breach}</h5>
                                <p className={`text-sm transition-colors duration-200 ${
                                  isDarkMode ? 'text-slate-300' : 'text-slate-600'
                                }`}>{breach.industry}</p>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-orange-300 flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                {breach.xposed_date || 'Unknown'}
                              </div>
                              <div className="text-sm text-gray-400">
                                {(breach.xposed_records || 0).toLocaleString()} records
                              </div>
                            </div>
                          </div>
                          <p className="text-sm text-gray-300 mb-3">{breach.details || 'No description available'}</p>
                          <div className="flex flex-wrap gap-2 mb-2">
                            {(breach.xposed_data || '').split(';').filter(Boolean).map((field, idx) => (
                              <span key={idx} className="px-2 py-1 bg-orange-800/50 text-orange-200 text-xs rounded-full">
                                {field.trim()}
                              </span>
                            ))}
                          </div>
                          <div className="flex items-center space-x-4 text-xs">
                            <span className={`flex items-center ${breach.verified === 'Yes' ? 'text-green-400' : 'text-gray-400'}`}>
                              <Check className="h-3 w-3 mr-1" />
                              {breach.verified === 'Yes' ? 'Verified' : 'Unverified'}
                            </span>
                            <span className="text-gray-400">
                              Password Risk: {breach.password_risk || 'Unknown'}
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {/* Score Section */}
        <div className={`rounded-2xl p-6 mb-8 border transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <div className="flex items-center justify-between mb-4">
            <h2 className={`text-2xl font-bold transition-colors duration-200 ${
              isDarkMode ? 'text-white' : 'text-slate-900'
            }`}>Your Hackability Score</h2>
            <div className="flex items-center">
              {score >= 50 ? (
                <AlertTriangle className="h-6 w-6 text-red-400 mr-2" />
              ) : (
                <div className="w-6 h-6 mr-2 flex items-center justify-center">
                  <img 
                    src="https://images.pexels.com/photos/1624496/pexels-photo-1624496.jpeg?auto=compress&cs=tinysrgb&w=24&h=24&fit=crop" 
                    alt="Cyber Ducky" 
                    className="w-6 h-6 rounded-full object-cover"
                  />
                </div>
              )}
              <span className={`text-3xl font-bold ${getScoreColor(score)}`}>
                {score}%
              </span>
            </div>
          </div>
          
          <div className="mb-4">
            <div className={`flex justify-between text-sm mb-2 transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              <span>Progress: {cleanedCount} of {totalItems} items cleaned</span>
              <span className={getScoreColor(score)}>{getScoreStatus(score)}</span>
            </div>
            <div className={`w-full rounded-full h-3 transition-colors duration-200 ${
              isDarkMode ? 'bg-slate-700' : 'bg-slate-200'
            }`}>
              <div 
                className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>
          
          <p className="text-gray-300 text-sm">
            Lower scores mean better privacy protection. Check each item and mark as "cleaned" when you've secured it.
          </p>
        </div>

        {/* Sections moved to About page */}

        {/* Checklist */}
        <div className={`rounded-xl p-8 mb-12 border shadow-sm transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <h2 className={`text-2xl font-semibold mb-8 transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>Privacy & Security Checklist</h2>

          <div className="space-y-6">
            {items.map((item) => {
              const isChecked = checkedIds.includes(item.id);

              return (
                <div
                  key={item.id}
                  className={`rounded-lg p-6 border transition-all duration-200 ${
                    isDarkMode
                      ? 'bg-slate-700 border-slate-600 hover:bg-slate-600'
                      : 'bg-slate-50 border-slate-200 hover:bg-slate-100'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 mr-4">
                      <h3 className={`text-xl font-semibold mb-3 transition-colors duration-200 ${
                        isDarkMode ? 'text-white' : 'text-slate-900'
                      }`}>
                        {item.title}
                      </h3>
                      <p className={`leading-relaxed mb-4 transition-colors duration-200 ${
                        isDarkMode ? 'text-slate-300' : 'text-slate-600'
                      }`}>
                        {item.description}
                      </p>

                      <div className="flex items-center space-x-4">
                        <button
                          onClick={() => openCheck(item)}
                          disabled={!formValid}
                          className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                            !formValid
                              ? isDarkMode
                                ? 'bg-slate-600 text-slate-400 cursor-not-allowed'
                                : 'bg-slate-300 text-slate-500 cursor-not-allowed'
                              : 'bg-blue-600 hover:bg-blue-700 text-white'
                          }`}
                        >
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Check
                        </button>

                        <button
                          onClick={() => toggleChecked(item.id)}
                          className={`flex items-center px-4 py-2 rounded-lg transition-all duration-200 ${
                            isChecked
                              ? 'bg-green-600 hover:bg-green-700 text-white'
                              : isDarkMode
                                ? 'bg-slate-600 hover:bg-slate-500 text-slate-300'
                                : 'bg-slate-200 hover:bg-slate-300 text-slate-700'
                          }`}
                        >
                          {isChecked ? (
                            <>
                              <Check className="h-4 w-4 mr-2" />
                              Completed ✓
                            </>
                          ) : (
                            <>
                              <X className="h-4 w-4 mr-2" />
                              Mark Complete
                            </>
                          )}
                        </button>
                      </div>
                    </div>

                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      isChecked
                        ? 'bg-green-600 border-green-600'
                        : isDarkMode
                          ? 'border-slate-500'
                          : 'border-slate-300'
                    }`}>
                      {isChecked && <Check className="h-4 w-4 text-white" />}
                    </div>
                  </div>
                </div>
              );
          })}
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`rounded-2xl p-6 mb-8 border transition-colors duration-200 ${
          isDarkMode
            ? 'bg-slate-800 border-slate-700'
            : 'bg-white border-slate-200'
        }`}>
          <h3 className={`text-xl font-bold mb-6 text-center transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>Take Action to Protect Your Privacy</h3>

          {/* Primary Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-6">
            <a
              href="https://www.de33watrk.com/48HP5T/KMKS9/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 font-semibold"
            >
              <Trash2 className="h-5 w-5 mr-2" />
              Delete Me - Remove Your Data
            </a>
            <a
              href="https://cyber-ducky.com/links/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-semibold"
            >
              <Users className="h-5 w-5 mr-2" />
              Need Help?
            </a>
          </div>

          {/* Security Tools - Affiliate Links */}
          <div className="border-t pt-6">
            <h4 className={`text-lg font-semibold mb-4 text-center transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-700'
            }`}>Recommended Security Tools</h4>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="https://go.nordvpn.net/aff_c?offer_id=15&aff_id=128661&url_id=902"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 font-semibold"
              >
                <Shield className="h-5 w-5 mr-2" />
                Secure Your Connection - NordVPN
              </a>
              <a
                href="https://go.nordpass.io/aff_c?offer_id=488&aff_id=128661&url_id=9356"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200 font-semibold"
              >
                <Key className="h-5 w-5 mr-2" />
                Manage Passwords - NordPass
              </a>
            </div>
            <p className={`text-xs text-center mt-3 transition-colors duration-200 ${
              isDarkMode ? 'text-slate-500' : 'text-slate-400'
            }`}>
              * These are affiliate links. We may earn a commission if you make a purchase.
            </p>
          </div>
        </div>

        {/* Social Sharing Section */}
        {searchInitiated && (
          <div className="mt-12">
            <SocialShare
              title="How Hackable Am I? - Cybersecurity Assessment"
              description={`I scored ${score}% on my cybersecurity assessment! Check how hackable you are and protect yourself online.`}
              url={window.location.href}
              score={score}
            />
          </div>
        )}

        {/* TikTok Content Section */}
        <div className="mt-16">
          <h2 className={`text-2xl font-bold mb-8 text-center transition-colors duration-200 ${
            isDarkMode ? 'text-white' : 'text-slate-900'
          }`}>
            <Video className="h-6 w-6 inline mr-2 text-blue-500" />
            Cybersecurity Tips in Action
          </h2>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Example TikTok embeds - replace with real video IDs */}
            <LazyLoad height={600} className="content-visibility-auto">
              <TikTokEmbed
                videoId="7234567890123456789"
                username="cyberducky"
                description="🔒 Quick tip: Enable 2FA on all your accounts! #cybersecurity #safety #tech"
                autoplay={shouldAutoplayVideo(deviceInfo)}
                muted={true}
              />
            </LazyLoad>
            <LazyLoad height={600} className="content-visibility-auto">
              <TikTokEmbed
                videoId="7234567890123456790"
                username="cyberducky"
                description="🛡️ How to spot phishing emails in 30 seconds #phishing #security #tips"
                autoplay={shouldAutoplayVideo(deviceInfo)}
                muted={true}
              />
            </LazyLoad>
            <LazyLoad height={600} className="content-visibility-auto">
              <TikTokEmbed
                videoId="7234567890123456791"
                username="cyberducky"
                description="📱 Your phone's hidden security settings you NEED to know #mobile #security"
                autoplay={shouldAutoplayVideo(deviceInfo)}
                muted={true}
              />
            </LazyLoad>
          </div>

          <div className="text-center mt-8">
            <a
              href="https://www.tiktok.com/@cyberducky"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-6 py-3 bg-black hover:bg-gray-800 text-white rounded-lg font-semibold transition-colors duration-200"
            >
              <Video className="h-5 w-5 mr-2" />
              Follow @CyberDucky on TikTok
            </a>
          </div>
        </div>

        {/* Footer */}
        <footer className={`mt-20 py-12 border-t transition-colors duration-200 ${
          isDarkMode
            ? 'border-slate-700 bg-slate-800'
            : 'border-slate-200 bg-slate-50'
        }`}>
          <div className="text-center">
            <p className={`mb-6 text-lg transition-colors duration-200 ${
              isDarkMode ? 'text-slate-300' : 'text-slate-600'
            }`}>
              Support Hackable's Development
            </p>
            <div className="flex justify-center items-center space-x-8 mb-6">
              <a
                href="https://buymeacoffee.com/cyberducky"
                target="_blank"
                rel="noopener noreferrer"
                className={`flex items-center hover:text-blue-500 transition-colors duration-200 px-4 py-2 rounded-lg border shadow-sm ${
                  isDarkMode
                    ? 'text-slate-300 bg-slate-700 border-slate-600 hover:bg-slate-600'
                    : 'text-slate-700 bg-white border-slate-200 hover:text-blue-600'
                }`}
              >
                <Coffee className="h-5 w-5 mr-2" />
                Support Us
              </a>
              <a
                href="https://account.venmo.com/u/cyberducky"
                target="_blank"
                rel="noopener noreferrer"
                className={`hover:text-blue-500 transition-colors duration-200 px-4 py-2 rounded-lg border shadow-sm ${
                  isDarkMode
                    ? 'text-slate-300 bg-slate-700 border-slate-600 hover:bg-slate-600'
                    : 'text-slate-700 bg-white border-slate-200 hover:text-blue-600'
                }`}
              >
                Venmo: @cyberducky
              </a>
            </div>
            <p className={`text-sm transition-colors duration-200 ${
              isDarkMode ? 'text-slate-400' : 'text-slate-500'
            }`}>
              Built with care by the CyberDucky team
            </p>
          </div>
        </footer>
      </div>

      {/* Terms and Conditions Modal */}
      <Terms isOpen={showTerms} onClose={() => setShowTerms(false)} />
    </div>
  );
};

export default HackabilityChecker;